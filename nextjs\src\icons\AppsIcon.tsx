const AppIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 19 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M9.49958 8.90928C9.38055 8.9091 9.26261 8.88654 9.15191 8.84279L0.653957 5.47058C0.455416 5.38657 0.286834 5.24463 0.170238 5.0633C0.0536428 4.88196 -0.00554307 4.66968 0.000414588 4.45417C-0.00557273 4.23916 0.0534084 4.02733 0.169658 3.84635C0.285907 3.66536 0.454023 3.52363 0.652057 3.43966L9.15381 0.06556C9.3767 -0.0218228 9.62436 -0.0218228 9.84725 0.06556L18.3452 3.43966C18.5436 3.52334 18.7121 3.66494 18.8287 3.84595C18.9453 4.02695 19.0046 4.23894 18.9987 4.45417C19.0047 4.66968 18.9455 4.88196 18.8289 5.0633C18.7123 5.24463 18.5437 5.38657 18.3452 5.47058L9.84535 8.84279C9.73524 8.88631 9.61797 8.90886 9.49958 8.90928ZM1.47089 4.45607L9.49958 7.64019L17.5264 4.45417L9.49958 1.27005L1.47089 4.45607ZM9.49958 16.5732C9.38036 16.5731 9.26228 16.5499 9.15191 16.5048L0.653957 13.1326C0.459546 13.0435 0.294807 12.9004 0.179313 12.7204C0.0638197 12.5404 0.00242588 12.331 0.00242588 12.1172C0.00242588 11.9033 0.0638197 11.6939 0.179313 11.5139C0.294807 11.3339 0.459546 11.1908 0.653957 11.1017C0.783738 11.0473 0.928091 11.0385 1.06352 11.0767C1.19896 11.1148 1.3175 11.1976 1.39986 11.3117C1.48223 11.4258 1.52356 11.5644 1.51714 11.7049C1.51071 11.8455 1.45692 11.9797 1.36449 12.0858L9.49958 15.3022L17.5264 12.1181L14.158 10.7882C14.0025 10.7273 13.8777 10.607 13.8109 10.454C13.7441 10.301 13.7408 10.1277 13.8018 9.97224C13.8627 9.8168 13.9829 9.69194 14.136 9.62514C14.289 9.55833 14.4623 9.55505 14.6177 9.61602L18.3452 11.0941C18.5403 11.1826 18.7057 11.3255 18.8217 11.5056C18.9377 11.6857 18.9994 11.8953 18.9994 12.1096C18.9994 12.3238 18.9377 12.5334 18.8217 12.7135C18.7057 12.8936 18.5403 13.0365 18.3452 13.125L9.84535 16.5067C9.73524 16.5502 9.61797 16.5728 9.49958 16.5732Z" />
            <path d="M9.4996 12.7412C9.38037 12.7411 9.2623 12.7179 9.15193 12.6728L0.653973 9.30253C0.459561 9.2134 0.294822 9.07033 0.179329 8.89032C0.0638352 8.71032 0.00244141 8.50094 0.00244141 8.28707C0.00244141 8.0732 0.0638352 7.86382 0.179329 7.68382C0.294822 7.50381 0.459561 7.36074 0.653973 7.27161L4.38335 5.79164C4.45981 5.76157 4.54146 5.74687 4.62361 5.74836C4.70576 5.74984 4.78682 5.7675 4.86214 5.80031C4.93747 5.83312 5.0056 5.88045 5.06264 5.9396C5.11968 5.99874 5.16451 6.06854 5.19457 6.14501C5.22464 6.22148 5.23934 6.30312 5.23786 6.38527C5.23637 6.46742 5.21871 6.54848 5.1859 6.62381C5.15309 6.69914 5.10576 6.76726 5.04661 6.8243C4.98747 6.88134 4.91767 6.92617 4.84121 6.95624L1.4709 8.28612L9.4996 11.4702L17.6347 8.24052C17.5464 8.13409 17.4962 8.00125 17.492 7.86305C17.4877 7.72485 17.5297 7.58919 17.6112 7.47754C17.6928 7.36589 17.8093 7.28464 17.9422 7.24665C18.0752 7.20866 18.217 7.21611 18.3452 7.26781C18.5404 7.35601 18.706 7.49863 18.8221 7.67858C18.9383 7.85852 19 8.06815 19 8.28232C19 8.49649 18.9383 8.70612 18.8221 8.88606C18.706 9.06601 18.5404 9.20863 18.3452 9.29683L9.84537 12.6747C9.73526 12.7183 9.61799 12.7408 9.4996 12.7412Z" />
        </svg>
    );
};

export default AppIcon;
