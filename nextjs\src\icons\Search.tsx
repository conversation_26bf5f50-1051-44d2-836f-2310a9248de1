
type SearchIconProps = {
    height?: number;
    width?: number;
    className?: string;
}

const SearchIcon = ({ height, width, className }: SearchIconProps) => {
    return (
        <svg
            className={className}  width={width} height={height}
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M7.17959 0.50293C5.27642 0.50612 3.45212 1.26356 2.10638 2.60931C0.760633 3.95505 0.00319056 5.77935 0 7.68251C0.0015906 9.58728 0.758136 11.4138 2.10388 12.7618C3.44962 14.1098 5.27482 14.8694 7.17959 14.8742C8.8689 14.8742 10.4255 14.2799 11.6563 13.2965L14.6458 16.2859C14.7972 16.4268 14.9973 16.5034 15.2041 16.4998C15.4109 16.4962 15.6082 16.4126 15.7546 16.2665C15.901 16.1205 15.9852 15.9234 15.9893 15.7166C15.9935 15.5099 15.9174 15.3096 15.777 15.1577L12.7875 12.1652C13.8085 10.8954 14.3651 9.31492 14.3652 7.68553C14.3652 3.72771 11.1374 0.50293 7.17959 0.50293ZM7.17959 2.10174C10.2746 2.10174 12.7664 4.59047 12.7664 7.68251C12.7664 10.7746 10.2746 13.2784 7.17959 13.2784C4.08452 13.2784 1.5958 10.7836 1.5958 7.68855C1.5958 4.59348 4.08452 2.10174 7.17959 2.10174Z" />
        </svg>
    );
};

export default SearchIcon;
