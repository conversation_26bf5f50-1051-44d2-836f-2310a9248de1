const AddBrainIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 160 151"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M79.7809 91.7206C78.3399 91.7206 76.958 91.1482 75.939 90.1293C74.9201 89.1103 74.3477 87.7284 74.3477 86.2874V63.9821C74.3477 62.5411 74.9201 61.1591 75.939 60.1402C76.958 59.1213 78.3399 58.5488 79.7809 58.5488C81.2219 58.5488 82.6039 59.1213 83.6228 60.1402C84.6417 61.1591 85.2142 62.5411 85.2142 63.9821V86.2874C85.2142 87.7284 84.6417 89.1103 83.6228 90.1293C82.6039 91.1482 81.2219 91.7206 79.7809 91.7206Z" />
            <path d="M79.7809 114.019C78.3399 114.019 76.958 113.446 75.939 112.427C74.9201 111.408 74.3477 110.026 74.3477 108.585V86.2799C74.3477 84.8389 74.9201 83.457 75.939 82.438C76.958 81.4191 78.3399 80.8467 79.7809 80.8467C81.2219 80.8467 82.6039 81.4191 83.6228 82.438C84.6417 83.457 85.2142 84.8389 85.2142 86.2799V108.585C85.2142 110.026 84.6417 111.408 83.6228 112.427C82.6039 113.446 81.2219 114.019 79.7809 114.019Z" />
            <path d="M79.7815 91.721H57.4762C56.0352 91.721 54.6533 91.1486 53.6343 90.1296C52.6154 89.1107 52.043 87.7287 52.043 86.2877C52.043 84.8468 52.6154 83.4648 53.6343 82.4459C54.6533 81.4269 56.0352 80.8545 57.4762 80.8545H79.7815C81.2225 80.8545 82.6045 81.4269 83.6234 82.4459C84.6424 83.4648 85.2148 84.8468 85.2148 86.2877C85.2148 87.7287 84.6424 89.1107 83.6234 90.1296C82.6045 91.1486 81.2225 91.721 79.7815 91.721Z" />
            <path d="M102.086 91.721H79.7809C78.3399 91.721 76.958 91.1486 75.939 90.1296C74.9201 89.1107 74.3477 87.7287 74.3477 86.2877C74.3477 84.8468 74.9201 83.4648 75.939 82.4459C76.958 81.4269 78.3399 80.8545 79.7809 80.8545H102.086C103.527 80.8545 104.909 81.4269 105.928 82.4459C106.947 83.4648 107.519 84.8468 107.519 86.2877C107.519 87.7287 106.947 89.1107 105.928 90.1296C104.909 91.1486 103.527 91.721 102.086 91.721Z" />
            <path d="M126.247 150.262H33.3167C24.4842 150.254 16.0155 146.743 9.76927 140.498C3.52304 134.253 0.00959099 125.785 0 116.953V24.015C0.00767209 17.6469 2.54127 11.542 7.04486 7.03974C11.5484 2.53751 17.6542 0.00575139 24.0222 0H47.7909C57.9619 0 67.4448 4.55669 73.8053 12.5037L78.4417 18.2992C79.6658 19.8439 81.2242 21.0909 82.9997 21.9467C84.7752 22.8024 86.7215 23.2445 88.6924 23.2398H126.254C135.086 23.2513 143.552 26.7656 149.795 33.0117C156.038 39.2577 159.549 47.7253 159.557 56.5565V116.96C159.547 125.792 156.033 134.26 149.787 140.505C143.541 146.75 135.072 150.262 126.24 150.269L126.247 150.262ZM24.0222 10.8665C20.5355 10.8703 17.1927 12.2566 14.7265 14.7214C12.2604 17.1862 10.8723 20.5283 10.8665 24.015V116.953C10.8742 122.904 13.2422 128.609 17.4509 132.816C21.6597 137.024 27.3656 139.39 33.3167 139.396H126.247C132.198 139.388 137.902 137.021 142.111 132.814C146.319 128.607 148.688 122.903 148.697 116.953V56.5493C148.692 50.5994 146.326 44.8946 142.121 40.6861C137.915 36.4775 132.211 34.1087 126.262 34.0991H88.6997C85.0989 34.1066 81.5431 33.2991 78.2988 31.7371C75.0544 30.1752 72.2056 27.8993 69.9658 25.0799L65.3294 19.2917C63.2307 16.6574 60.5638 14.5315 57.5281 13.0726C54.4924 11.6137 51.1662 10.8595 47.7981 10.8665H24.0222Z" />
        </svg>
    );
};

export default AddBrainIcon;


