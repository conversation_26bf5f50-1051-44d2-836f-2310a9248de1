type DesktopIconProps = {
    height: number;
    width: number;
    className: string;
}

const DesktopIcon = ({ height, width, className }: DesktopIconProps) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 138 124"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.268 96.3345C15.8045 96.3345 10.5648 94.1641 6.70157 90.3009C2.83832 86.4376 0.667969 81.198 0.667969 75.7345V20.8012C0.667969 15.3377 2.83832 10.098 6.70157 6.23477C10.5648 2.37152 15.8045 0.201172 21.268 0.201172H117.401C122.865 0.201172 128.104 2.37152 131.968 6.23477C135.831 10.098 138.001 15.3377 138.001 20.8012V75.7345C138.001 81.198 135.831 86.4376 131.968 90.3009C128.104 94.1641 122.865 96.3345 117.401 96.3345H21.268ZM14.4013 75.7345C14.4013 77.5556 15.1248 79.3022 16.4125 80.59C17.7003 81.8777 19.4468 82.6012 21.268 82.6012H117.401C119.222 82.6012 120.969 81.8777 122.257 80.59C123.545 79.3022 124.268 77.5556 124.268 75.7345V20.8012C124.268 18.98 123.545 17.2335 122.257 15.9457C120.969 14.658 119.222 13.9345 117.401 13.9345H21.268C19.4468 13.9345 17.7003 14.658 16.4125 15.9457C15.1248 17.2335 14.4013 18.98 14.4013 20.8012V75.7345Z"
            />
            <path d="M96.8019 123.802H41.8686C40.0475 123.802 38.3009 123.078 37.0132 121.79C35.7254 120.503 35.002 118.756 35.002 116.935C35.002 115.114 35.7254 113.367 37.0132 112.08C38.3009 110.792 40.0475 110.068 41.8686 110.068H96.8019C98.6231 110.068 100.37 110.792 101.657 112.08C102.945 113.367 103.669 115.114 103.669 116.935C103.669 118.756 102.945 120.503 101.657 121.79C100.37 123.078 98.6231 123.802 96.8019 123.802Z" />
        </svg>
    );
};

export default DesktopIcon;
