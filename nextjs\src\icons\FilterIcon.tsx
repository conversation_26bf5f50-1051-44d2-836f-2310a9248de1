
import React from 'react';

const FilterIcon = ({height, width, className}:any) => {
  return (
    <svg className={className}  width={width} height={height} viewBox="0 0 13 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M0.0292964 5.38531C0.0292965 6.64403 0.931891 7.70298 2.12106 7.94667L2.12106 14.7982C2.12106 14.9369 2.17615 15.0699 2.27422 15.168C2.37229 15.2661 2.5053 15.3212 2.644 15.3212C2.78269 15.3212 2.9157 15.2661 3.01377 15.168C3.11184 15.0699 3.16694 14.9369 3.16694 14.7982L3.16694 7.94667C4.35662 7.70298 5.2587 6.64455 5.2587 5.38531C5.2587 4.12607 4.35662 3.06764 3.16694 2.82395L3.16694 1.20179C3.16694 1.0631 3.11184 0.930087 3.01377 0.832017C2.9157 0.733947 2.78269 0.678852 2.644 0.678852C2.5053 0.678852 2.37229 0.733947 2.27422 0.832017C2.17615 0.930087 2.12106 1.0631 2.12106 1.20179L2.12106 2.82395C0.931891 3.06764 0.0292964 4.12607 0.0292964 5.38531ZM1.07518 5.38531C1.07518 4.51252 1.77121 3.81649 2.644 3.81649C3.51678 3.81649 4.21282 4.51252 4.21282 5.38531C4.21282 6.2581 3.51678 6.95413 2.644 6.95413C1.77121 6.95413 1.07518 6.2581 1.07518 5.38531ZM6.7999 10.6147C6.7999 11.8734 7.70249 12.9324 8.89166 13.1761L8.89166 14.7982C8.89166 14.9369 8.94675 15.0699 9.04482 15.168C9.14289 15.2661 9.2759 15.3212 9.4146 15.3212C9.55329 15.3212 9.6863 15.2661 9.78437 15.168C9.88244 15.0699 9.93754 14.9369 9.93754 14.7982L9.93754 13.1761C11.1272 12.9324 12.0293 11.8739 12.0293 10.6147C12.0293 9.35547 11.1272 8.29704 9.93754 8.05335L9.93754 1.20179C9.93754 1.0631 9.88244 0.930087 9.78437 0.832017C9.6863 0.733946 9.55329 0.678852 9.4146 0.678852C9.2759 0.678852 9.14289 0.733946 9.04482 0.832017C8.94675 0.930087 8.89166 1.0631 8.89166 1.20179L8.89166 8.05335C7.70249 8.29704 6.7999 9.35547 6.7999 10.6147ZM7.84578 10.6147C7.84578 9.74192 8.54181 9.04589 9.4146 9.04589C10.2874 9.04589 10.9834 9.74192 10.9834 10.6147C10.9834 11.4875 10.2874 12.1835 9.4146 12.1835C8.54181 12.1835 7.84578 11.4875 7.84578 10.6147Z"/>
    </svg>
  );
}

export default FilterIcon;