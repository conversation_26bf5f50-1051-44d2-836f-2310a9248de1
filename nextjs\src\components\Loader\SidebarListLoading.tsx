import React from 'react';

const SidebarListLoading = () => {
    return (
        <div className="sidebar-sub-menu-items pb-6 px-2.5 animate-pulse">
            <div className="my-2.5">
                <div className="flex items-center py-2.5 px-5 rounded-custom bg-gray-300">
                    <div className="size-[18px] bg-gray-400/50 rounded-full mr-2.5"></div>
                    <div className="w-20 h-5 rounded-custom bg-gray-400/50"></div>
                </div>
                <div className="my-[15px]">
                    <div className="flex flex-col gap-y-[15px]">
                        <div className="collapse-menu-item">
                            <div className="flex items-center">
                                <div className="w-full h-5 rounded-custom bg-gray-300"></div>
                            </div>
                        </div>
                        <div className="collapse-menu-item">
                            <div className="flex items-center">
                                <div className="w-full h-5 rounded-custom bg-gray-300"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex items-center px-1 my-5">
                    <div className="w-5 h-5 bg-gray-300 rounded-full mr-2.5"></div>
                    <div className="w-full h-5 bg-gray-300 rounded-custom"></div>
                </div>
            </div>
            <div className="my-2.5">
                <div className="flex items-center py-2.5 px-5 rounded-custom bg-gray-300">
                    <div className="size-[18px] bg-gray-400/50 rounded-full mr-2.5"></div>
                    <div className="w-20 h-5 rounded-custom bg-gray-400/50"></div>
                </div>
                <div className="my-[15px]">
                    <div className="flex flex-col gap-y-[15px]">
                        <div className="collapse-menu-item">
                            <div className="flex items-center">
                                <div className="w-full h-5 rounded-custom bg-gray-300"></div>
                            </div>
                        </div>
                        <div className="collapse-menu-item">
                            <div className="flex items-center">
                                <div className="w-full h-5 rounded-custom bg-gray-300"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="flex items-center px-1 my-5">
                    <div className="w-5 h-5 bg-gray-300 rounded-full mr-2.5"></div>
                    <div className="w-full h-5 bg-gray-300 rounded-custom"></div>
                </div>
            </div>
        </div>
    );
};

export default SidebarListLoading;
