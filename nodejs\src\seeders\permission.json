[{"roleCode": "COMPANY", "permissions": ["check.apikey", "workspace.list", "user.list", "bot.list", "customgpt.delete", "auth.invite", "customgpt.view", "workspaceuser.create", "workspace.create", "customgpt.update", "customgpt.list", "workspaceuser.list", "workspace.update", "workspace.delete", "customgpt.create", "workspace.view", "billing.list", "workspaceuser.delete", "user.delete", "toggle.create", "team.create", "team.update", "team.delete", "payment.invoiceList", "storagerequest.list", "storagerequest.decline", "storagerequest.approve", "invoice.list", "report.companyUsage", "report.userUsage", "report.weeklyUsage", "creditControl.addCreditById"]}, {"roleCode": "MANAGER", "permissions": ["check.apikey", "user.list", "user.delete", "billing.list", "auth.invite", "bot.list", "workspace.update", "workspaceuser.list", "workspaceuser.delete", "workspaceuser.create", "workspace.create", "workspace.delete", "toggle.create", "team.create", "team.update", "team.delete", "report.companyUsage", "report.userUsage", "report.weeklyUsage", "payment.invoiceList", "storagerequest.list", "storagerequest.decline", "storagerequest.approve", "invoice.list", "creditControl.addCreditById"]}]