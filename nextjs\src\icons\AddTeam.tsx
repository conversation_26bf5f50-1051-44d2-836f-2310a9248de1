const AddTeam = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 25 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M18.9224 9.03196C18.5789 8.86852 18.2256 8.72668 17.8644 8.60723C19.0275 7.73423 19.7812 6.34412 19.7812 4.78125C19.7812 2.14488 17.6364 0 15 0C12.3636 0 10.2187 2.14488 10.2187 4.78125C10.2187 6.34584 10.9742 7.73733 12.1394 8.61015C11.0718 8.96164 10.07 9.50857 9.19323 10.2298C7.58546 11.5522 6.4676 13.3969 6.04562 15.4239C5.91336 16.059 6.07181 16.7113 6.48026 17.2136C6.8867 17.7134 7.48938 18 8.13372 18H18.3187C18.707 18 19.0218 17.6852 19.0218 17.2969C19.0218 16.9085 18.707 16.5938 18.3187 16.5938H8.13372C7.83356 16.5938 7.65247 16.4262 7.57126 16.3264C7.43102 16.1539 7.37674 15.9294 7.42234 15.7104C8.15201 12.2055 11.2435 9.64526 14.8136 9.55891C14.9384 9.56374 15.0634 9.56372 15.1882 9.55884C16.2823 9.58451 17.3345 9.83419 18.3187 10.302C18.6694 10.4687 19.0888 10.3196 19.2555 9.96884C19.4223 9.61812 19.2731 9.19867 18.9224 9.03196ZM15.1715 8.15193C15.0576 8.14982 14.9437 8.14985 14.8298 8.152C13.0476 8.06309 11.625 6.58519 11.625 4.78125C11.625 2.92025 13.139 1.40625 15 1.40625C16.861 1.40625 18.375 2.92025 18.375 4.78125C18.375 6.58473 16.9531 8.06235 15.1715 8.15193Z" />
            <path d="M9.37679 10.0249C9.12755 9.89774 8.87116 9.78741 8.60911 9.69452C9.45306 9.01552 10 7.93432 10 6.71875C10 4.66824 8.44365 3 6.53061 3C4.61757 3 3.06122 4.66824 3.06122 6.71875C3.06122 7.93566 3.60938 9.01792 4.45492 9.69678C3.68022 9.97017 2.95331 10.3956 2.31708 10.9565C1.15045 11.9851 0.339302 13.4198 0.0331025 14.9963C-0.062867 15.4903 0.0521076 15.9977 0.348485 16.3883C0.643409 16.7771 1.08073 17 1.54828 17H6C6.28179 17 6.5102 16.7552 6.5102 16.4531C6.5102 16.1511 6.28179 15.9062 6 15.9062H1.54828C1.33048 15.9062 1.19907 15.7759 1.14014 15.6983C1.03838 15.5642 0.998996 15.3896 1.03208 15.2192C1.56155 12.4932 3.80482 10.5019 6.39535 10.4347C6.48593 10.4385 6.57661 10.4384 6.66719 10.4347C7.46107 10.4546 8.22459 10.6488 8.93872 11.0127C9.19324 11.1423 9.49758 11.0263 9.61855 10.7535C9.73952 10.4808 9.6313 10.1545 9.37679 10.0249ZM6.65505 9.34039C6.57242 9.33875 6.48977 9.33877 6.40714 9.34044C5.1139 9.27129 4.08163 8.12181 4.08163 6.71875C4.08163 5.27131 5.18023 4.09375 6.53061 4.09375C7.88099 4.09375 8.97959 5.27131 8.97959 6.71875C8.97959 8.12146 7.94783 9.27071 6.65505 9.34039Z" />
            <path d="M23.9766 12.6367H22.043V10.7031C22.043 10.3148 21.7282 10 21.3398 10C20.9515 10 20.6367 10.3148 20.6367 10.7031V12.6367H18.7031C18.3148 12.6367 18 12.9515 18 13.3398C18 13.7282 18.3148 14.043 18.7031 14.043H20.6367V15.9766C20.6367 16.3649 20.9515 16.6797 21.3398 16.6797C21.7282 16.6797 22.043 16.3649 22.043 15.9766V14.043H23.9766C24.3649 14.043 24.6797 13.7282 24.6797 13.3398C24.6797 12.9515 24.3649 12.6367 23.9766 12.6367Z" />
        </svg>
    );
};

export default AddTeam;
