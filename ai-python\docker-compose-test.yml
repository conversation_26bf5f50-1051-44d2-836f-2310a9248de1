services:
  test:
    build:
      context: .
      dockerfile: Dockerfile-test
    container_name: pytest_service
    env_file: ${ENV_FILE}
    command: ["sh", "-c", "pytest --maxfail=1 --disable-warnings --html=/src/reports/test_report_${WEAM_ENVIRONMENT}_$(date +'%Y-%m-%d_%H-%M-%S').html"]
    # command: ["sh", "-c", "export TZ=Asia/Kolkata && pytest --maxfail=1 --disable-warnings --html=/src/reports/test_report_$(date +'%Y-%m-%d_%H-%M-%S').html"]
    # command: ["sh", "-c", "pytest -k 'test_openai_store_vector_valid_input' --maxfail=1 --disable-warnings --html=/src/reports/test_report_$(date +'%Y-%m-%d_%H-%M-%S').html"]
    networks:
      - custom_network
    volumes:
      - logs:/src/logs
      - reports:/src/reports
      - ./test_reports:/src/reports 

volumes:
  redis_data:
  logs:
  reports:

networks:
  custom_network:
    external: true