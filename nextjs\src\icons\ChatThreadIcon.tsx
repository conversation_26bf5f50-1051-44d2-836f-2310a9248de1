import React from 'react';

const ChatThreadIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.3636 0H3.63634C1.63126 0 0 1.63126 0 3.63634V9.45448C0.00136105 10.2923 0.291402 11.104 0.821273 11.753C1.35114 12.4019 2.08847 12.8485 2.90907 13.0174V15.2726C2.90905 15.4043 2.94478 15.5335 3.01243 15.6464C3.08009 15.7594 3.17715 15.8519 3.29324 15.914C3.40933 15.9761 3.54011 16.0055 3.67162 15.9991C3.80313 15.9928 3.93045 15.9508 4.03997 15.8777L8.22031 13.0908H12.3636C14.3686 13.0908 15.9999 11.4596 15.9999 9.45448V3.63634C15.9999 1.63126 14.3686 0 12.3636 0ZM7.59631 11.7585L4.36361 13.9134V12.3636C4.36361 12.1707 4.28698 11.9857 4.15059 11.8493C4.01421 11.7129 3.82922 11.6363 3.63634 11.6363C2.43344 11.6363 1.45454 10.6574 1.45454 9.45448V3.63634C1.45454 2.43344 2.43344 1.45454 3.63634 1.45454H12.3636C13.5665 1.45454 14.5454 2.43344 14.5454 3.63634V9.45448C14.5454 10.6574 13.5665 11.6363 12.3636 11.6363H7.99995C7.85596 11.634 7.71486 11.6767 7.59631 11.7585Z"/>
    <path d="M12.4545 4H3.72727C3.53438 4 3.3494 4.07662 3.21301 4.21301C3.07662 4.3494 3 4.53438 3 4.72727C3 4.92015 3.07662 5.10513 3.21301 5.24152C3.3494 5.37791 3.53438 5.45454 3.72727 5.45454H12.4545C12.6474 5.45454 12.8323 5.37791 12.9687 5.24152C13.1051 5.10513 13.1817 4.92015 13.1817 4.72727C13.1817 4.53438 13.1051 4.3494 12.9687 4.21301C12.8323 4.07662 12.6474 4 12.4545 4ZM10.9999 6.90907H5.1818C4.98892 6.90907 4.80394 6.98569 4.66755 7.12208C4.53116 7.25847 4.45454 7.44346 4.45454 7.63634C4.45454 7.82922 4.53116 8.01421 4.66755 8.15059C4.80394 8.28698 4.98892 8.36361 5.1818 8.36361H10.9999C11.1928 8.36361 11.3778 8.28698 11.5142 8.15059C11.6506 8.01421 11.7272 7.82922 11.7272 7.63634C11.7272 7.44346 11.6506 7.25847 11.5142 7.12208C11.3778 6.98569 11.1928 6.90907 10.9999 6.90907Z"/>
    </svg>

  );
}

export default ChatThreadIcon;
