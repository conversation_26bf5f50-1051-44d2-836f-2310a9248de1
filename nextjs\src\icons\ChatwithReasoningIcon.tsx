import React from 'react';

const ChatwithReasoningIcon = ({ width, height, className }: any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H16.6667C18.5076 0 20 1.49238 20 3.33333V16.6667C20 18.5076 18.5076 20 16.6667 20H3.33333C1.49238 20 0 18.5076 0 16.6667V3.33333Z"
                fill="#BFC3F6"
            />
            <g clipPath="url(#clip0_827_560)">
                <path
                    d="M9.85484 6.2243C9.8387 6.19611 9.80514 6.15071 9.73472 6.09344C9.60555 5.98848 9.40898 5.89471 9.18394 5.85712C8.75914 5.78624 8.288 5.91316 7.99547 6.49774C7.87304 6.74251 7.58907 6.85966 7.32945 6.77313C6.991 6.66022 6.48388 6.81629 6.1068 7.27704C5.75232 7.7103 5.60802 8.31357 5.91246 8.92255C5.97036 9.03856 5.98441 9.16995 5.95543 9.29364L5.91246 9.41376C5.73117 9.7765 5.62255 10.1577 5.64586 10.53C5.66079 10.7683 5.73238 11.0231 5.89879 11.2888C6.26638 11.0028 6.80536 10.7018 7.50328 10.7018C7.80699 10.7019 8.05308 10.9479 8.05308 11.2516C8.05307 11.5554 7.80698 11.8014 7.50328 11.8015C6.97007 11.8015 6.5833 12.1259 6.30211 12.4001C6.24478 12.6039 6.28586 12.9645 6.50328 13.2907C6.72693 13.6259 7.0141 13.7513 7.32945 13.6462L7.44078 13.6218C7.55295 13.6089 7.66731 13.6311 7.76793 13.6862C7.90221 13.7599 8.00027 13.8863 8.03746 14.0349C8.19557 14.6666 8.56093 14.9373 8.95055 15.0436C9.25675 15.1272 9.59101 15.1083 9.86949 15.0358V10.3298C9.56938 10.4604 9.20033 10.5512 8.75328 10.5515C8.44971 10.5516 8.20385 10.3062 8.20347 10.0026C8.20328 9.69902 8.44875 9.45228 8.7523 9.45184C9.24389 9.45153 9.54129 9.29089 9.71129 9.15497C9.78215 9.09831 9.83326 9.04182 9.86949 8.99774L9.87047 8.33466L9.86949 6.25165L9.85484 6.2243ZM9.30406 13.3356C9.3037 13.6393 9.0569 13.8856 8.75328 13.8854C8.4496 13.8851 8.20329 13.6383 8.20347 13.3347L9.30406 13.3356ZM7.94762 11.696C8.13543 11.508 8.42549 11.4849 8.63902 11.6257L8.72594 11.696L8.82945 11.8063C9.31305 12.361 9.30446 12.9369 9.30406 13.3356L8.75328 13.3347H8.20347C8.20383 12.9778 8.19616 12.7748 8.02965 12.5651L7.94859 12.4743L7.8773 12.3874C7.7366 12.174 7.75991 11.8839 7.94762 11.696ZM6.95347 9.16864C6.95347 9.02485 6.94995 8.70899 7.10875 8.36493C7.27311 8.0089 7.58083 7.68162 8.09117 7.42645C8.36283 7.29071 8.69265 7.4009 8.82847 7.67255C8.96423 7.94406 8.85467 8.27384 8.58336 8.40985C8.26043 8.57132 8.15079 8.73055 8.1068 8.82587C8.05731 8.93315 8.05406 9.0347 8.05406 9.16864C8.05388 9.47225 7.80693 9.71844 7.50328 9.71844C7.19978 9.71827 6.95365 9.47214 6.95347 9.16864ZM10.9701 7.78485H11.4427L12.1146 7.11298C12.3294 6.89819 12.6781 6.89819 12.8929 7.11298C13.1073 7.32768 13.1073 7.67561 12.8929 7.89032L12.0589 8.7243C11.9558 8.82725 11.816 8.88544 11.6703 8.88544H10.9701V10.2849H12.9203L13.0316 10.2956C13.282 10.347 13.47 10.5691 13.4701 10.8347C13.4701 11.1003 13.282 11.3223 13.0316 11.3737L12.9203 11.3845H10.9701V12.3679H11.6703L11.7777 12.3786C11.8835 12.3996 11.9815 12.4517 12.0589 12.529L12.8929 13.363C13.1073 13.5777 13.1073 13.9256 12.8929 14.1403C12.6781 14.3551 12.3294 14.3551 12.1146 14.1403L11.4427 13.4684H10.9701V15.4186C10.97 15.6268 10.8516 15.8167 10.6654 15.9099C10.1402 16.1725 9.37727 16.3003 8.66148 16.1052C8.03271 15.9337 7.46029 15.5152 7.13609 14.7839C6.4535 14.7947 5.90768 14.3807 5.58726 13.9001C5.27739 13.435 5.09509 12.7907 5.21519 12.2155C4.78783 11.6987 4.58261 11.1481 4.5482 10.5983C4.51538 10.0732 4.64065 9.58374 4.81969 9.1618C4.44698 8.18919 4.71854 7.23674 5.25523 6.58075C5.72392 6.00791 6.4655 5.58711 7.22887 5.64227C7.79164 4.85879 8.6408 4.65141 9.36461 4.77216C9.76428 4.83884 10.1408 5.00556 10.4281 5.23895C10.7036 5.46281 10.97 5.81252 10.9701 6.25165V7.78485Z"
                    fill="#7559CB"
                />
                <path
                    d="M14.0322 14.377C14.0322 14.1055 13.8124 13.8849 13.541 13.8848C13.2695 13.8848 13.0488 14.1054 13.0488 14.377C13.049 14.6483 13.2696 14.8682 13.541 14.8682C13.8123 14.868 14.0321 14.6482 14.0322 14.377ZM15.1328 14.377C15.1326 15.2557 14.4198 15.9686 13.541 15.9688C12.6621 15.9688 11.9494 15.2559 11.9492 14.377C11.9492 13.4979 12.662 12.7852 13.541 12.7852C14.4199 12.7853 15.1328 13.498 15.1328 14.377Z"
                    fill="#7559CB"
                />
                <path
                    d="M14.8682 10.791C14.8682 10.5196 14.6483 10.299 14.377 10.2988C14.1054 10.2988 13.8848 10.5195 13.8848 10.791C13.8849 11.0624 14.1055 11.2822 14.377 11.2822C14.6482 11.2821 14.868 11.0623 14.8682 10.791ZM15.9688 10.791C15.9686 11.6698 15.2557 12.3826 14.377 12.3828C13.498 12.3828 12.7853 11.6699 12.7852 10.791C12.7852 9.91196 13.4979 9.19922 14.377 9.19922C15.2559 9.19939 15.9688 9.91207 15.9688 10.791Z"
                    fill="#7559CB"
                />
                <path
                    d="M14.0322 6.87695C14.0322 6.60552 13.8124 6.38494 13.541 6.38477C13.2695 6.38477 13.0488 6.60541 13.0488 6.87695C13.049 7.14834 13.2696 7.36816 13.541 7.36816C13.8123 7.36799 14.0321 7.14823 14.0322 6.87695ZM15.1328 6.87695C15.1326 7.75575 14.4198 8.46857 13.541 8.46875C12.6621 8.46875 11.9494 7.75586 11.9492 6.87695C11.9492 5.9979 12.662 5.28516 13.541 5.28516C14.4199 5.28533 15.1328 5.99801 15.1328 6.87695Z"
                    fill="#7559CB"
                />
            </g>
            <defs>
                <clipPath id="clip0_827_560">
                    <rect
                        width="12.5"
                        height="12.5"
                        fill="white"
                        transform="translate(4.16797 4.16602)"
                    />
                </clipPath>
            </defs>
        </svg>
    );
};

export default ChatwithReasoningIcon;
