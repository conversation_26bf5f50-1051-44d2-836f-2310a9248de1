const ShareIcon = ({width, height, className}:any) => {
    return (
        <svg
            width={width} height={height} className={className}
            viewBox="0 0 114 125"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M91.2288 79.5465C87.6473 79.5548 84.1186 80.4085 80.9308 82.0378C77.743 83.6671 74.9866 86.0258 72.8865 88.9213L44.3371 69.9274C46.1363 65.1396 46.1363 59.8633 44.3371 55.0755L72.8865 36.0816C76.091 40.4175 80.7396 43.4708 85.9972 44.6929C91.2548 45.9151 96.7768 45.2259 101.571 42.7494C106.365 40.2728 110.117 36.1711 112.154 31.1812C114.19 26.1914 114.376 20.6404 112.679 15.5256C110.983 10.4108 107.514 6.06753 102.897 3.27604C98.2798 0.484541 92.8165 -0.572168 87.4887 0.295764C82.1609 1.1637 77.3178 3.89938 73.8295 8.0113C70.3413 12.1232 68.4366 17.3419 68.4575 22.7296C68.4945 23.5575 68.5781 24.3827 68.708 25.2012L38.0181 45.6211C34.7475 42.6785 30.6922 40.7455 26.3434 40.0561C21.9945 39.3667 17.5387 39.9505 13.5155 41.7368C9.49226 43.5231 6.07429 46.4353 3.6755 50.1207C1.27671 53.8062 0 58.1067 0 62.5014C0 66.8962 1.27671 71.1967 3.6755 74.8821C6.07429 78.5675 9.49226 81.4797 13.5155 83.2661C17.5387 85.0524 21.9945 85.6362 26.3434 84.9468C30.6922 84.2574 34.7475 82.3243 38.0181 79.3817L68.708 99.8017C68.5781 100.62 68.4945 101.445 68.4575 102.273C68.4575 106.768 69.7931 111.162 72.2952 114.9C74.7973 118.637 78.3537 121.55 82.5146 123.27C86.6755 124.99 91.254 125.44 95.6712 124.563C100.088 123.686 104.146 121.522 107.33 118.343C110.515 115.165 112.684 111.116 113.562 106.707C114.441 102.298 113.99 97.7289 112.267 93.5761C110.543 89.4233 107.624 85.8739 103.88 83.3767C100.135 80.8794 95.7325 79.5465 91.2288 79.5465ZM91.2288 11.3662C93.4806 11.3662 95.6819 12.0327 97.5543 13.2813C99.4266 14.5299 100.886 16.3047 101.748 18.381C102.609 20.4574 102.835 22.7422 102.396 24.9465C101.956 27.1508 100.872 29.1755 99.2796 30.7647C97.6873 32.3539 95.6586 33.4362 93.45 33.8747C91.2414 34.3131 88.9521 34.0881 86.8717 33.228C84.7912 32.3679 83.013 30.9115 81.762 29.0428C80.5109 27.1741 79.8432 24.9771 79.8432 22.7296C79.8432 19.7159 81.0427 16.8255 83.1779 14.6945C85.3131 12.5635 88.2091 11.3662 91.2288 11.3662ZM22.9151 73.8648C20.6632 73.8648 18.462 73.1984 16.5896 71.9497C14.7172 70.7011 13.2579 68.9264 12.3962 66.85C11.5344 64.7736 11.309 62.4888 11.7483 60.2845C12.1876 58.0803 13.272 56.0555 14.8643 54.4663C16.4566 52.8771 18.4853 51.7949 20.6939 51.3564C22.9025 50.9179 25.1917 51.143 27.2722 52.003C29.3526 52.8631 31.1308 54.3196 32.3819 56.1883C33.633 58.057 34.3007 60.254 34.3007 62.5014C34.3007 65.5152 33.1012 68.4055 30.9659 70.5366C28.8307 72.6676 25.9348 73.8648 22.9151 73.8648ZM91.2288 113.637C88.9769 113.637 86.7756 112.97 84.9033 111.722C83.0309 110.473 81.5716 108.698 80.7098 106.622C79.8481 104.545 79.6226 102.261 80.0619 100.056C80.5012 97.8521 81.5856 95.8273 83.1779 94.2381C84.7702 92.6489 86.799 91.5667 89.0075 91.1282C91.2161 90.6898 93.5054 90.9148 95.5859 91.7749C97.6663 92.6349 99.4445 94.0914 100.696 95.9601C101.947 97.8288 102.614 100.026 102.614 102.273C102.614 105.287 101.415 108.177 99.2796 110.308C97.1444 112.439 94.2484 113.637 91.2288 113.637Z" />
        </svg>
    );
};

export const ShareIcon2 = ({width, height, className}:any) => {
    return (
        <svg width={width} height={height} className={className}
            viewBox="0 0 22 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M17.4817 15.2729C16.7954 15.2745 16.1192 15.4384 15.5084 15.7513C14.8975 16.0641 14.3693 16.5169 13.9669 17.0729L8.4961 13.4261C8.84086 12.5068 8.84086 11.4938 8.4961 10.5745L13.9669 6.92766C14.5809 7.76015 15.4717 8.34639 16.4792 8.58104C17.4867 8.81569 18.5449 8.68338 19.4636 8.20788C20.3822 7.73238 21.1013 6.94485 21.4914 5.9868C21.8816 5.02874 21.9173 3.96295 21.5922 2.98092C21.2671 1.99888 20.6024 1.16497 19.7177 0.628999C18.8329 0.0930318 17.786 -0.109856 16.765 0.0567868C15.7441 0.22343 14.816 0.748681 14.1476 1.53817C13.4792 2.32766 13.1142 3.32964 13.1182 4.36409C13.1253 4.52304 13.1413 4.68147 13.1662 4.83862L7.28522 8.75926C6.65849 8.19428 5.8814 7.82313 5.04805 7.69076C4.2147 7.5584 3.36085 7.67049 2.5899 8.01346C1.81895 8.35644 1.16399 8.91558 0.704317 9.62318C0.244649 10.3308 0 11.1565 0 12.0003C0 12.8441 0.244649 13.6698 0.704317 14.3774C1.16399 15.085 1.81895 15.6441 2.5899 15.9871C3.36085 16.3301 4.2147 16.4422 5.04805 16.3098C5.8814 16.1774 6.65849 15.8063 7.28522 15.2413L13.1662 19.1619C13.1413 19.3191 13.1253 19.4775 13.1182 19.6365C13.1182 20.4995 13.3741 21.3431 13.8536 22.0607C14.333 22.7783 15.0145 23.3376 15.8119 23.6678C16.6092 23.9981 17.4866 24.0845 18.333 23.9162C19.1794 23.7478 19.957 23.3322 20.5672 22.7219C21.1775 22.1117 21.593 21.3342 21.7614 20.4877C21.9298 19.6413 21.8434 18.7639 21.5131 17.9666C21.1828 17.1693 20.6235 16.4878 19.906 16.0083C19.1884 15.5288 18.3447 15.2729 17.4817 15.2729ZM17.4817 2.18232C17.9132 2.18232 18.3351 2.31028 18.6938 2.55001C19.0526 2.78975 19.3323 3.13049 19.4974 3.52916C19.6625 3.92783 19.7057 4.36651 19.6216 4.78973C19.5374 5.21295 19.3296 5.6017 19.0245 5.90683C18.7193 6.21196 18.3306 6.41975 17.9074 6.50393C17.4841 6.58812 17.0455 6.54491 16.6468 6.37978C16.2481 6.21464 15.9074 5.935 15.6676 5.57621C15.4279 5.21742 15.2999 4.7956 15.2999 4.36409C15.2999 3.78545 15.5298 3.2305 15.939 2.82134C16.3481 2.41218 16.9031 2.18232 17.4817 2.18232ZM4.39111 14.182C3.9596 14.182 3.53777 14.0541 3.17898 13.8143C2.82019 13.5746 2.54055 13.2339 2.37542 12.8352C2.21028 12.4365 2.16708 11.9979 2.25126 11.5746C2.33545 11.1514 2.54324 10.7627 2.84837 10.4575C3.15349 10.1524 3.54224 9.94461 3.96547 9.86043C4.38869 9.77624 4.82737 9.81945 5.22603 9.98458C5.6247 10.1497 5.96545 10.4294 6.20518 10.7881C6.44492 11.1469 6.57288 11.5688 6.57288 12.0003C6.57288 12.5789 6.34301 13.1339 5.93385 13.543C5.52469 13.9522 4.96975 14.182 4.39111 14.182ZM17.4817 21.8182C17.0502 21.8182 16.6284 21.6903 16.2696 21.4505C15.9108 21.2108 15.6312 20.8701 15.466 20.4714C15.3009 20.0727 15.2577 19.634 15.3419 19.2108C15.4261 18.7876 15.6338 18.3988 15.939 18.0937C16.2441 17.7886 16.6329 17.5808 17.0561 17.4966C17.4793 17.4124 17.918 17.4556 18.3166 17.6208C18.7153 17.7859 19.0561 18.0655 19.2958 18.4243C19.5355 18.7831 19.6635 19.2049 19.6635 19.6365C19.6635 20.2151 19.4336 20.77 19.0245 21.1792C18.6153 21.5884 18.0604 21.8182 17.4817 21.8182Z" />
        </svg>
    );
};

export const ShareBrain = () => {
    return (
        <svg
            width="198"
            height="181"
            viewBox="0 0 198 181"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M179.023 100.088C178.642 69.495 160.944 43.0053 135.267 29.9114L131.625 43.1954C151.171 54.2095 164.621 74.7728 165.54 98.5006C151.844 100.256 141.219 111.952 141.219 126.097C141.219 141.439 153.727 153.929 169.115 153.929C184.503 153.929 197.011 141.45 197.011 126.097C197.011 114.233 189.536 104.092 179.034 100.088H179.023ZM169.115 140.343C161.236 140.343 154.825 133.947 154.825 126.086C154.825 118.225 161.236 111.829 169.115 111.829C176.994 111.829 183.405 118.225 183.405 126.086C183.405 133.947 176.994 140.343 169.115 140.343Z" />
            <path d="M73.5009 39.6731C77.9728 49.1105 87.6003 55.6519 98.7297 55.6519C114.107 55.6519 126.626 43.173 126.626 27.8203C126.626 12.4677 114.107 0 98.7297 0C83.3525 0 71.6852 11.6626 70.8782 26.2996C43.5984 36.531 23.357 61.1869 19.5464 90.9081H33.1079C36.7056 67.6947 52.3741 48.4172 73.5009 39.6731ZM98.7297 13.5747C106.609 13.5747 113.02 19.9707 113.02 27.8315C113.02 35.6923 106.609 42.0883 98.7297 42.0883C90.8506 42.0883 84.4397 35.6923 84.4397 27.8315C84.4397 19.9707 90.8506 13.5747 98.7297 13.5747Z" />
            <path d="M98.9539 167.582C78.8919 167.582 60.8809 158.681 48.6644 144.626C53.0803 139.706 55.7814 133.198 55.7814 126.086C55.7814 110.745 43.2734 98.2546 27.8851 98.2546C12.4967 98.2546 0 110.745 0 126.086C0 141.428 12.5079 153.918 27.8963 153.918C31.2698 153.918 34.4977 153.314 37.5014 152.218C52.1948 169.785 74.2855 181 98.9651 181C120.036 181 139.235 172.826 153.547 159.497L143.628 150.395C131.804 161.063 116.136 167.582 98.9763 167.582H98.9539ZM13.6063 126.086C13.6063 118.225 20.0172 111.829 27.8963 111.829C35.7754 111.829 42.1863 118.225 42.1863 126.086C42.1863 133.947 35.7754 140.343 27.8963 140.343C20.0172 140.343 13.6063 133.947 13.6063 126.086Z" />
        </svg>
    );
};

export const ShareBrainIcon = ({height, width, className}:any) => {
    return (
        <svg className={className} width={width} height={height}
            viewBox="0 0 20 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M7.35318 3.94539C7.79687 4.88392 8.75208 5.53444 9.8563 5.53444C11.382 5.53444 12.6241 4.29345 12.6241 2.76666C12.6241 1.23988 11.382 0 9.8563 0C8.33064 0 7.17304 1.15982 7.09298 2.61543C4.38636 3.63292 2.37808 6.08488 2 9.04059H3.34552C3.70248 6.73207 5.25706 4.81497 7.35318 3.94539ZM9.8563 1.34997C10.638 1.34997 11.2741 1.98604 11.2741 2.76778C11.2741 3.54952 10.638 4.18558 9.8563 4.18558C9.07456 4.18558 8.4385 3.54952 8.4385 2.76778C8.4385 1.98604 9.07456 1.34997 9.8563 1.34997ZM17.7027 9.97893C17.6649 6.93649 15.909 4.30216 13.3614 3L13 4.32106C14.9393 5.41638 16.2737 7.46136 16.3649 9.82103C15.0061 9.99561 13.9519 11.1588 13.9519 12.5655C13.9519 14.0911 15.1929 15.3332 16.7197 15.3332C18.2464 15.3332 19.4874 14.0922 19.4874 12.5655C19.4874 11.3856 18.7457 10.377 17.7038 9.97893H17.7027ZM16.7197 13.9821C15.9379 13.9821 15.3018 13.3461 15.3018 12.5643C15.3018 11.7826 15.9379 11.1465 16.7197 11.1465C17.5014 11.1465 18.1375 11.7826 18.1375 12.5643C18.1375 13.3461 17.5014 13.9821 16.7197 13.9821ZM4.82832 14.6115C6.0404 16.0093 7.82739 16.8944 9.81788 16.8944H9.8201C11.5226 16.8944 13.0772 16.2461 14.2503 15.1853L15.2344 16.0904C13.8144 17.416 11.9096 18.2288 9.81899 18.2288C7.37036 18.2288 5.1786 17.1135 3.72076 15.3665C3.42275 15.4755 3.10249 15.5356 2.76778 15.5356C1.241 15.5356 0 14.2934 0 12.7678C0 11.2421 1.23988 10 2.76666 10C4.29345 10 5.53444 11.2421 5.53444 12.7678C5.53444 13.475 5.26645 14.1222 4.82832 14.6115ZM2.76778 11.35C1.98604 11.35 1.34997 11.986 1.34997 12.7678C1.34997 13.5495 1.98604 14.1856 2.76778 14.1856C3.54952 14.1856 4.18558 13.5495 4.18558 12.7678C4.18558 11.986 3.54952 11.35 2.76778 11.35Z"
            />
        </svg>
    );
};

export default ShareIcon;
