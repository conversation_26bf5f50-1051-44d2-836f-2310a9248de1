import React from 'react';

const InitiateThreadsIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 425 425" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M365.064 0H59.9359C26.8731 0 0 30.0987 0 67.1064V208.86C0 227.059 10.1782 242.577 25.9141 248.396C30.0115 249.922 34.1962 250.663 38.3154 250.663C48.6244 250.663 58.5628 246.064 65.9513 237.455L81.3603 219.518C86.4167 213.612 92.9551 210.364 99.7769 210.364H365.064C398.127 210.364 425 180.265 425 143.258V67.1282C425 30.1205 398.127 0 365.064 0ZM392.308 143.236C392.308 162.219 380.081 177.65 365.064 177.65H99.7769C83.5397 177.65 67.782 185.147 56.5577 198.203L41.1487 216.14C39.1872 218.406 37.9667 217.949 37.2692 217.731C35.0026 216.881 32.6923 213.808 32.6923 208.838V67.0846C32.6923 48.1013 44.9192 32.6705 59.9359 32.6705H365.064C380.081 32.6705 392.308 48.1013 392.308 67.0846V143.236ZM365.064 261.538H147.115C131.223 261.55 115.985 267.868 104.747 279.106C93.5094 290.344 87.191 305.582 87.1795 321.474V365.064C87.191 380.957 93.5094 396.195 104.747 407.432C115.985 418.67 131.223 424.988 147.115 425H365.064C380.957 424.988 396.195 418.67 407.432 407.432C418.67 396.195 424.988 380.957 425 365.064V321.474C424.988 305.582 418.67 290.344 407.432 279.106C396.195 267.868 380.957 261.55 365.064 261.538ZM392.308 365.064C392.308 380.081 380.081 392.308 365.064 392.308H147.115C132.099 392.308 119.872 380.081 119.872 365.064V321.474C119.872 306.458 132.099 294.231 147.115 294.231H365.064C380.081 294.231 392.308 306.458 392.308 321.474V365.064ZM337.821 103.526C337.821 107.861 336.098 112.019 333.033 115.084C329.967 118.15 325.81 119.872 321.474 119.872H103.526C99.1904 119.872 95.0327 118.15 91.9672 115.084C88.9017 112.019 87.1795 107.861 87.1795 103.526C87.1795 99.1904 88.9017 95.0327 91.9672 91.9672C95.0327 88.9017 99.1904 87.1795 103.526 87.1795H321.474C325.81 87.1795 329.967 88.9017 333.033 91.9672C336.098 95.0327 337.821 99.1904 337.821 103.526ZM337.821 343.269C337.821 347.604 336.098 351.762 333.033 354.828C329.967 357.893 325.81 359.615 321.474 359.615H190.705C186.37 359.615 182.212 357.893 179.147 354.828C176.081 351.762 174.359 347.604 174.359 343.269C174.359 338.934 176.081 334.776 179.147 331.711C182.212 328.645 186.37 326.923 190.705 326.923H321.474C325.81 326.923 329.967 328.645 333.033 331.711C336.098 334.776 337.821 338.934 337.821 343.269Z"/>
    </svg>
  );
}

export default InitiateThreadsIcon;
