import React from 'react';

const AudioUploadIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={height}
            height={width}
            viewBox="0 0 54 41"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M37.0302 39.3449C37.0302 39.5623 36.9874 39.7775 36.9042 39.9783C36.821 40.1791 36.6991 40.3615 36.5454 40.5152C36.3917 40.6689 36.2093 40.7908 36.0085 40.874C35.8077 40.9572 35.5924 41 35.3751 41C35.1578 41 34.9425 40.9572 34.7417 40.874C34.5409 40.7908 34.3585 40.6689 34.2048 40.5152C34.0511 40.3615 33.9292 40.1791 33.846 39.9783C33.7628 39.7775 33.72 39.5623 33.72 39.3449V1.78692C33.72 1.56957 33.7628 1.35434 33.846 1.15354C33.9292 0.952736 34.0511 0.770281 34.2048 0.616593C34.3585 0.462904 34.5409 0.34099 34.7417 0.257814C34.9425 0.174638 35.1578 0.13183 35.3751 0.13183C35.5924 0.13183 35.8077 0.174638 36.0085 0.257814C36.2093 0.34099 36.3917 0.462904 36.5454 0.616593C36.6991 0.770281 36.821 0.952736 36.9042 1.15354C36.9874 1.35434 37.0302 1.56957 37.0302 1.78692V39.3449ZM10.551 36.0353C11.4653 36.0353 12.2056 35.2939 12.2056 34.3802V6.75217C12.2096 6.5323 12.1698 6.31384 12.0884 6.10953C12.0071 5.90523 11.8858 5.71918 11.7318 5.56226C11.5777 5.40534 11.3939 5.2807 11.1912 5.19562C10.9884 5.11053 10.7707 5.06671 10.5508 5.06671C10.3309 5.06671 10.1132 5.11053 9.91039 5.19562C9.70761 5.2807 9.52382 5.40534 9.36976 5.56226C9.21569 5.71918 9.09445 5.90523 9.0131 6.10953C8.93175 6.31384 8.89193 6.5323 8.89596 6.75217V34.3797C8.89596 35.2939 9.63682 36.0353 10.551 36.0353ZM2.27618 27.7604C3.19041 27.7604 3.93126 27.0196 3.93126 26.1059V15.0259C3.93126 14.587 3.75689 14.166 3.4465 13.8556C3.13611 13.5452 2.71513 13.3708 2.27618 13.3708C1.83722 13.3708 1.41625 13.5452 1.10586 13.8556C0.795468 14.166 0.621094 14.587 0.621094 15.0259V26.1065C0.621094 27.0207 1.36251 27.761 2.27618 27.761V27.7604ZM20.4804 27.7604C20.4804 28.1994 20.3061 28.6204 19.9957 28.9308C19.6853 29.2411 19.2643 29.4155 18.8254 29.4155C18.3864 29.4155 17.9654 29.2411 17.655 28.9308C17.3446 28.6204 17.1703 28.1994 17.1703 27.7604V13.3714C17.1703 12.9324 17.3446 12.5115 17.655 12.2011C17.9654 11.8907 18.3864 11.7163 18.8254 11.7163C19.2643 11.7163 19.6853 11.8907 19.9957 12.2011C20.3061 12.5115 20.4804 12.9324 20.4804 13.3714V27.7604ZM27.1002 32.7251C28.0145 32.7251 28.7553 31.9843 28.7553 31.0706V10.0612C28.7553 9.84388 28.7125 9.62866 28.6293 9.42785C28.5462 9.22705 28.4242 9.04459 28.2706 8.8909C28.1169 8.73722 27.9344 8.6153 27.7336 8.53213C27.5328 8.44895 27.3176 8.40614 27.1002 8.40614C26.8829 8.40614 26.6677 8.44895 26.4669 8.53213C26.2661 8.6153 26.0836 8.73722 25.9299 8.8909C25.7762 9.04459 25.6543 9.22705 25.5711 9.42785C25.488 9.62866 25.4451 9.84388 25.4451 10.0612V31.0706C25.4451 31.9848 26.1866 32.7251 27.1002 32.7251ZM43.65 32.7251C44.5642 32.7251 45.3045 31.9843 45.3045 31.0706V10.0612C45.3085 9.84136 45.2687 9.62289 45.1874 9.41858C45.106 9.21428 44.9848 9.02824 44.8307 8.87132C44.6766 8.7144 44.4928 8.58976 44.2901 8.50467C44.0873 8.41959 43.8696 8.37577 43.6497 8.37577C43.4298 8.37577 43.2121 8.41959 43.0093 8.50467C42.8065 8.58976 42.6227 8.7144 42.4687 8.87132C42.3146 9.02824 42.1934 9.21428 42.112 9.41858C42.0307 9.62289 41.9908 9.84136 41.9949 10.0612V31.0706C41.9949 31.9848 42.7357 32.7251 43.65 32.7251ZM53.5794 29.4155C53.5794 29.8545 53.405 30.2755 53.0946 30.5858C52.7842 30.8962 52.3632 31.0706 51.9243 31.0706C51.4853 31.0706 51.0643 30.8962 50.754 30.5858C50.4436 30.2755 50.2692 29.8545 50.2692 29.4155V11.7163C50.2692 11.2774 50.4436 10.8564 50.754 10.546C51.0643 10.2356 51.4853 10.0612 51.9243 10.0612C52.3632 10.0612 52.7842 10.2356 53.0946 10.546C53.405 10.8564 53.5794 11.2774 53.5794 11.7163V29.4155Z"
            />
        </svg>
    );
};

export default AudioUploadIcon;
