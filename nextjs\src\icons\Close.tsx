const Close = ({height, width, className}:any) => {
    return (
        <svg className={className} width={width} height={height}
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.7075 0.305288C13.5201 0.117817 13.266 0.0125018 13.0011 0.0125018C12.7361 0.0125018 12.4821 0.117817 12.2947 0.305288L7.00625 5.59829L1.71781 0.305288C1.62564 0.209778 1.51539 0.133596 1.39349 0.0811869C1.27159 0.0287779 1.14048 0.00119157 1.00782 3.7757e-05C0.875155 -0.00111606 0.74359 0.0241854 0.6208 0.0744663C0.49801 0.124747 0.386454 0.199 0.292642 0.292893C0.19883 0.386786 0.12464 0.498438 0.0744019 0.621334C0.0241642 0.74423 -0.00111485 0.87591 3.8147e-05 1.00869C0.00119114 1.14147 0.0287542 1.27269 0.0811176 1.39469C0.133481 1.5167 0.209598 1.62704 0.305025 1.71929L5.59346 7.01229L0.305025 12.3053C0.123024 12.4939 0.022316 12.7465 0.0245924 13.0087C0.0268688 13.2709 0.131948 13.5217 0.317197 13.7071C0.502445 13.8925 0.753042 13.9977 1.01501 14C1.27698 14.0022 1.52937 13.9014 1.71781 13.7193L7.00625 8.42629L12.2947 13.7193C12.4831 13.9014 12.7355 14.0022 12.9975 14C13.2595 13.9977 13.51 13.8925 13.6953 13.7071C13.8805 13.5217 13.9856 13.2709 13.9879 13.0087C13.9902 12.7465 13.8895 12.4939 13.7075 12.3053L8.41903 7.01229L13.7075 1.71929C13.8948 1.53176 14 1.27745 14 1.01229C14 0.747124 13.8948 0.492816 13.7075 0.305288Z"
            />
        </svg>
    );
};

export default Close;
