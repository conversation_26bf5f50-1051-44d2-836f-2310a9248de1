import React from 'react';

const PencilIcon = ({ width, height, className }:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 11 11"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M6.73694 1.82327L0.73462 7.83235C0.704395 7.86269 0.682854 7.90058 0.672243 7.94209L0.00692119 10.6153C-0.00286401 10.655 -0.00226434 10.6965 0.00866228 10.7359C0.0195889 10.7752 0.0404732 10.8111 0.0692979 10.84C0.113572 10.8842 0.173518 10.909 0.236027 10.9091C0.25531 10.9091 0.274519 10.9067 0.293221 10.902L2.96361 10.2359C3.00513 10.2254 3.04304 10.2039 3.07326 10.1735L9.07613 4.16488L6.73694 1.82327ZM10.5632 1.00394L9.895 0.335105C9.44844 -0.111924 8.67013 -0.11148 8.22408 0.335105L7.40563 1.15444L9.74471 3.49595L10.5632 2.67664C10.7862 2.45344 10.9091 2.15633 10.9091 1.84035C10.9091 1.52437 10.7862 1.22726 10.5632 1.00394Z" />
        </svg>
    );
};

export default PencilIcon;
