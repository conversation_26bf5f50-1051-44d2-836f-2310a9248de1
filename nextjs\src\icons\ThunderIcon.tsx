import React from 'react';

const ThunderIcon = ({ width, height, className }:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 72 108"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M32.6499 108C31.1783 107.995 29.7253 107.669 28.3916 107.046C26.2131 106.074 24.4634 104.339 23.471 102.167C22.4786 99.9945 22.3115 97.534 23.0013 95.247L30.0132 72.0001H13.492C11.3871 72.0026 9.31085 71.5112 7.42971 70.5651C5.54856 69.619 3.9148 68.2446 2.65935 66.5521C1.40391 64.8595 0.561693 62.8959 0.200209 60.8185C-0.161276 58.7412 -0.031979 56.6079 0.577739 54.5896L14.1612 9.58971C14.9892 6.80637 16.696 4.36713 19.0253 2.63807C21.3546 0.909022 24.1809 -0.0165673 27.08 0.000224502H43.6685C45.554 0.00180464 47.4111 0.46069 49.0811 1.33767C50.751 2.21464 52.1842 3.48363 53.2582 5.03618C54.3321 6.58873 55.0148 8.37869 55.248 10.2531C55.4812 12.1275 55.258 14.0305 54.5973 15.7997L47.0375 36.0002H58.5233C60.9716 36 63.3737 36.668 65.4716 37.9325C67.5695 39.197 69.2838 41.0101 70.4306 43.1772C71.5773 45.3443 72.113 47.7834 71.9801 50.2325C71.8473 52.6816 71.051 55.0482 69.6766 57.0781L40.9285 103.428C40.0398 104.822 38.8165 105.971 37.3703 106.77C35.9241 107.569 34.3012 107.992 32.6499 108ZM27.08 9.00021C26.1153 8.99927 25.176 9.30945 24.401 9.88486C23.626 10.4603 23.0565 11.2703 22.7767 12.1952L9.19321 57.1951C8.98983 57.8675 8.94643 58.5782 9.06647 59.2704C9.18652 59.9626 9.46667 60.617 9.88451 61.1812C10.3023 61.7455 10.8463 62.2039 11.4727 62.5197C12.0991 62.8356 12.7907 63.0001 13.492 63.0001H36.0638C36.765 63.0001 37.4566 63.1646 38.083 63.4805C38.7095 63.7963 39.2534 64.2547 39.6712 64.819C40.089 65.3832 40.3692 66.0376 40.4892 66.7298C40.6093 67.422 40.5659 68.1327 40.3625 68.8051L31.6168 97.794C31.5763 97.9007 31.5585 98.0146 31.5643 98.1286C31.5701 98.2425 31.5995 98.354 31.6506 98.456C31.7017 98.5579 31.7734 98.6481 31.8611 98.7209C31.9489 98.7936 32.0507 98.8473 32.1603 98.8785C32.2596 98.9423 32.3711 98.9845 32.4877 99.0025C32.6043 99.0204 32.7233 99.0137 32.8372 98.9828C32.951 98.9518 33.0571 98.8973 33.1486 98.8228C33.2402 98.7482 33.3151 98.6553 33.3686 98.55L62.1168 52.2001C62.6195 51.517 62.9192 50.7054 62.9811 49.859C63.0431 49.0126 62.865 48.1659 62.4672 47.4166C62.0985 46.6867 61.5343 46.0741 60.8378 45.6473C60.1412 45.2205 59.3398 44.9964 58.5233 45.0001H40.5557C39.8297 45.0006 39.1144 44.8247 38.4711 44.4877C37.8277 44.1506 37.2756 43.6623 36.8618 43.0647C36.4481 42.4671 36.1851 41.7779 36.0954 41.0562C36.0057 40.3344 36.0919 39.6017 36.3467 38.9206L46.184 12.6407C46.3352 12.2333 46.3859 11.7953 46.3318 11.364C46.2778 10.9327 46.1206 10.5209 45.8737 10.1635C45.6267 9.80606 45.2973 9.51367 44.9134 9.31115C44.5295 9.10863 44.1024 9.00196 43.6685 9.00021H27.08Z" />
        </svg>
    );
};

export default ThunderIcon;
