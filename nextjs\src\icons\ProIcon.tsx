import React from 'react';

const ProIcon = ({ width, height, className }:any) => {
    return (
        <svg className={className}
            width={width}
            height={height} viewBox="0 0 448 397" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M392.992 54.08C378.41 54.0969 364.43 59.8984 354.12 70.2112C343.81 80.5239 338.013 94.5056 338 109.088C338 121.216 342.064 132.336 348.736 141.44L311.136 191.552L262.016 94.592C267.375 89.4849 271.643 83.3437 274.561 76.5399C277.479 69.7361 278.986 62.4111 278.992 55.008C278.992 24.672 254.336 0 224 0C193.664 0 168.992 24.672 168.992 55.008C168.992 70.576 175.552 84.576 186 94.592L136.864 191.552L99.264 141.44C106.21 132.081 109.973 120.743 110 109.088C109.987 94.5029 104.188 80.5188 93.8745 70.2055C83.5612 59.8923 69.5771 54.0927 54.992 54.08C40.4096 54.0969 26.4296 59.8984 16.1198 70.2112C5.81006 80.5239 0.0127036 94.5056 0 109.088C0 136.128 19.632 158.528 45.376 163.104L64 318.464C64.4621 322.353 66.3347 325.938 69.2629 328.539C72.1911 331.139 75.9715 332.576 79.888 332.576H368.08C371.994 332.576 375.772 331.142 378.7 328.545C381.628 325.947 383.502 322.366 383.968 318.48L402.608 163.12C415.313 160.868 426.821 154.22 435.119 144.339C443.417 134.459 447.977 121.975 448 109.072C447.987 94.4869 442.188 80.5027 431.874 70.1895C421.561 59.8763 407.577 54.0927 392.992 54.08ZM392.992 132.08C380.32 132.08 370 121.776 370 109.088C370 96.4 380.32 86.08 392.992 86.08C398.91 86.3504 404.496 88.8918 408.589 93.1755C412.681 97.4592 414.965 103.156 414.965 109.08C414.965 115.004 412.681 120.701 408.589 124.984C404.496 129.268 398.91 131.81 392.992 132.08ZM94.096 300.56L78 166.4L126.704 231.328C130.016 235.776 135.504 238.288 140.896 237.68C143.606 237.444 146.21 236.52 148.464 234.997C150.718 233.474 152.546 231.402 153.776 228.976L214.544 109.056C217.632 109.584 220.768 110.016 224 110.016C227.232 110.016 230.368 109.584 233.44 109.056L294.24 228.976C295.469 231.4 297.294 233.471 299.545 234.994C301.796 236.516 304.397 237.441 307.104 237.68C312.608 238.272 317.984 235.76 321.296 231.328L370 166.4L353.904 300.576L94.096 300.56ZM32 109.104C32 96.416 42.32 86.096 54.992 86.096C60.9102 86.3664 66.4964 88.9078 70.5889 93.1915C74.6813 97.4752 76.965 103.172 76.965 109.096C76.965 115.02 74.6813 120.717 70.5889 125.001C66.4964 129.284 60.9102 131.826 54.992 132.096C48.894 132.088 43.0484 129.66 38.7379 125.347C34.4274 121.033 32.0042 115.202 32 109.104ZM200.992 55.024C200.992 42.336 211.328 32.016 224 32.016C236.672 32.016 246.992 42.336 246.992 55.024C246.992 67.712 236.672 78 224 78C211.328 78 200.992 67.712 200.992 55.024ZM368.096 396.576C372.339 396.576 376.409 394.89 379.41 391.89C382.41 388.889 384.096 384.819 384.096 380.576C384.096 376.333 382.41 372.263 379.41 369.262C376.409 366.262 372.339 364.576 368.096 364.576H79.904C75.6605 364.576 71.5909 366.262 68.5903 369.262C65.5897 372.263 63.904 376.333 63.904 380.576C63.904 384.819 65.5897 388.889 68.5903 391.89C71.5909 394.89 75.6605 396.576 79.904 396.576H368.096Z" />
</svg>
    );
};

export default ProIcon;
