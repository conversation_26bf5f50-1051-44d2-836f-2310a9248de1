services:
  app:
    build:
      context: .
      dockerfile: ./nextjs/Dockerfile  # Use the Dockerfile in the current directory
    container_name: weam-frontend-container
    ports:
      - "3000:3000"
    restart: always
    env_file:
      - .env  # Load environment variables from a .env file
    volumes:
      - .:/usr/src/app  # Mount the current directory into the container (useful for development)
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
