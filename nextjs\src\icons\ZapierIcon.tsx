const ZapierIcon = ({ className }) => {
    return (
        <svg
            className={className}
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M12.7041 0.391846C13.3991 0.393123 14.0827 0.451856 14.7472 0.56294V7.72591L19.8254 2.66076C20.3863 3.05912 20.911 3.50218 21.3961 3.9861C21.8815 4.47002 22.327 4.9948 22.7263 5.55404L17.6479 10.6192H24.8296C24.941 11.2819 24.9999 11.9611 24.9999 12.6557V12.6723C24.9999 13.3669 24.941 14.0475 24.8296 14.7101H17.6466L22.7263 19.774C22.327 20.3333 21.8815 20.858 21.3976 21.342H21.3961C20.911 21.8259 20.3863 22.2702 19.8269 22.6673L14.7472 17.6021V24.7651C14.0839 24.8762 13.4003 24.9349 12.7053 24.9363H12.6874C11.9921 24.9349 11.3113 24.8762 10.6468 24.7651V17.6021L5.56855 22.6673C4.44704 21.8718 3.46654 20.8925 2.66762 19.774L7.74608 14.7101H0.564385C0.451721 14.0462 0.392822 13.3631 0.392822 12.6685V12.6328C0.39399 12.4526 0.405793 12.2324 0.422746 12.0046L0.427981 11.936C0.478178 11.2948 0.564385 10.6192 0.564385 10.6192H7.74608L2.66762 5.55404C3.06574 4.9948 3.51004 4.47129 3.99516 3.98867L3.9976 3.9861C4.48174 3.50218 5.00767 3.05912 5.56855 2.66076L10.6468 7.72591V0.56294C11.3113 0.451856 11.9936 0.393123 12.6899 0.391846H12.7041ZM12.7028 9.59646H12.6911C11.7771 9.59646 10.9017 9.76372 10.0925 10.0676C9.7892 10.8733 9.6213 11.7466 9.62008 12.6583V12.6698C9.6213 13.5814 9.7892 14.4548 10.0937 15.2604C10.9017 15.5643 11.7771 15.7316 12.6911 15.7316H12.7028C13.6169 15.7316 14.4923 15.5643 15.3002 15.2604C15.6047 14.4535 15.7726 13.5814 15.7726 12.6698V12.6583C15.7726 11.7466 15.6047 10.8733 15.3002 10.0676C14.4923 9.76372 13.6169 9.59646 12.7028 9.59646Z"
                fill="#FF4A00"
            />
        </svg>
    );
};

export default ZapierIcon;
