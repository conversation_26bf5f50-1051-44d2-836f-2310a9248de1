const ChatMind = () => {
    return (
        <svg
            width="14"
            height="16"
            viewBox="0 0 14 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M0.56312 2.90908C0.621302 2.92944 0.699848 2.92071 0.752212 2.84508L1.37767 1.90544C1.3915 1.88585 1.40979 1.86983 1.43104 1.8587C1.45228 1.84758 1.47587 1.84166 1.49985 1.84144H5.07512C5.15367 1.84144 5.22058 1.90835 5.22058 1.98689V9.95199C5.22058 10.016 5.17694 10.0742 5.11585 10.0916L4.25476 10.3593C4.0453 10.4233 3.90858 10.6124 3.90858 10.8247C3.90858 10.9964 4.04821 11.1331 4.21985 11.1331H8.1704C8.64662 11.1037 8.51774 10.4506 8.13549 10.3593L7.27149 10.0916C7.24201 10.082 7.21632 10.0633 7.19804 10.0382C7.17977 10.0132 7.16984 9.98301 7.16967 9.95199V1.98689C7.16967 1.90835 7.23658 1.84144 7.31512 1.84144H10.8904C10.9399 1.84144 10.9864 1.86762 11.0126 1.90544L11.638 2.84508C11.6875 2.9178 11.7689 2.92944 11.8271 2.90908C11.8533 2.90326 11.9435 2.86544 11.9435 2.7549V0.878531C11.9435 0.392712 11.5449 -1.52588e-05 11.0562 -1.52588e-05H1.33403C0.842393 -1.52588e-05 0.443848 0.392712 0.443848 0.878531V2.7549C0.443848 2.86544 0.534029 2.90326 0.56312 2.90908Z" />
            <path d="M11.9743 9.41673H10.5227C10.4069 9.41673 10.2959 9.46271 10.2141 9.54454C10.1323 9.62638 10.0863 9.73737 10.0863 9.8531C10.0863 9.96883 10.1323 10.0798 10.2141 10.1617C10.2959 10.2435 10.4069 10.2895 10.5227 10.2895H11.9743C12.5291 10.2895 12.9806 10.734 12.9806 11.2809C12.9806 11.8278 12.5291 12.2729 11.974 12.2729H1.86578C-0.619455 12.3651 -0.6244 15.9055 1.86578 16H3.30957C3.4253 16 3.53629 15.954 3.61812 15.8722C3.69996 15.7904 3.74593 15.6794 3.74593 15.5636C3.74593 15.4479 3.69996 15.3369 3.61812 15.2551C3.53629 15.1733 3.4253 15.1273 3.30957 15.1273H1.86578C1.3116 15.1273 0.860401 14.6828 0.860401 14.1364C0.860401 13.5901 1.31131 13.1456 1.86578 13.1456H11.9746C14.4607 13.0534 14.4659 9.51099 11.9746 9.41644L11.9743 9.41673Z" />
        </svg>
    );
};

export default ChatMind;
