type MobileIconProps = {
    height: number;
    width: number;
    className: string;
}

const MobileIcon = ({ height, width, className }: MobileIconProps) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 69 124"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M54.5688 0.5H54.5682L13.9758 0.5L13.9752 0.5C6.53577 0.508369 0.507559 6.53682 0.5 13.9763V13.9768L0.5 110.023L0.5 110.024C0.507289 117.463 6.53606 123.491 13.9753 123.499H13.9758H54.5682H54.5687C62.0083 123.492 68.0364 117.463 68.045 110.024V110.023V13.9768V13.9762C68.0367 6.53679 62.0082 0.508639 54.5688 0.5ZM61.4989 110.023C61.4951 113.849 58.3941 116.95 54.5677 116.954H13.9763C10.15 116.95 7.0492 113.849 7.04507 110.023C7.04507 110.023 7.04507 110.023 7.04507 110.023V13.9773C7.04507 13.9771 7.04507 13.977 7.04507 13.9768C7.04923 10.1497 10.1498 7.04923 13.9758 7.04507C13.9759 7.04507 13.9761 7.04507 13.9763 7.04507H54.5677C54.5679 7.04507 54.5681 7.04507 54.5682 7.04507C58.3944 7.04923 61.4951 10.15 61.4989 13.9773V110.023Z"
                fill="black"
                stroke="black"
            />
            <path
                d="M50.5114 16H18.0326C16.5081 16 15.2715 17.3425 15.2715 18.9994C15.2715 20.6564 16.5079 22 18.0326 22H50.5114C52.0359 22 53.2715 20.6564 53.2715 18.9994C53.2715 17.3425 52.0361 16 50.5114 16Z"
                fill="black"
            />
            <path
                d="M34.2722 106C38.4142 106 41.7715 102.642 41.7715 98.4993C41.7715 94.3573 38.4138 91 34.2722 91C30.1288 91 26.7715 94.3577 26.7715 98.4993C26.7715 102.643 30.1292 106 34.2722 106Z"
                fill="black"
            />
        </svg>
    );
};

export default MobileIcon;
