import React from 'react';

const AtIcon = ({height, width, className}:any) => {
  return (
    <svg width={width} height={height} className={className} viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path className={className} d="M9.04202 10.0442C9.42943 9.54613 9.65162 8.81342 9.7023 7.83956L9.04202 10.0442ZM9.04202 10.0442C8.66257 10.5337 8.11239 10.7805 7.38354 10.7805C6.74417 10.7805 6.26469 10.5782 5.93665 10.1792C5.61269 9.77273 5.44688 9.17449 5.44688 8.37687C5.44688 7.76611 5.55892 7.22455 5.78172 6.7511C6.00428 6.27816 6.32639 5.90628 6.74832 5.63423C7.17471 5.36293 7.6885 5.22614 8.29197 5.22614C8.60117 5.22614 8.88734 5.24582 9.15058 5.28502C9.3992 5.32205 9.62203 5.37393 9.81933 5.44043L9.7023 7.83956L9.04202 10.0442ZM8.86196 11.119C9.3033 10.8411 9.64591 10.4379 9.8905 9.91177C9.9425 10.3905 10.1218 10.7807 10.4307 11.0787C10.7697 11.3943 11.2077 11.5512 11.7389 11.5512C12.2281 11.5512 12.6538 11.436 13.0139 11.2037C13.3777 10.9727 13.6773 10.6554 13.9129 10.2531C14.1537 9.85175 14.3309 9.39102 14.4451 8.87153C14.5591 8.35266 14.6161 7.80569 14.6161 7.23072C14.6161 5.95605 14.3571 4.8521 13.8371 3.92071C13.3174 2.98979 12.586 2.26984 11.6438 1.7616C10.7013 1.25325 9.59482 1 8.32593 1C7.22265 1 6.22029 1.17346 5.31952 1.52121C4.42465 1.86321 3.6545 2.3594 3.00972 3.00987C2.36474 3.65487 1.86864 4.4393 1.5211 5.36228C1.17338 6.28006 1 7.31346 1 8.46177C1 9.82638 1.25588 11.0062 1.76969 11.9996C2.28916 12.9929 3.02906 13.7587 3.98859 14.2956C4.95398 14.8326 6.10331 15.1 7.43448 15.1C8.13362 15.1 8.78513 15.0375 9.38885 14.9122C9.99155 14.7871 10.5437 14.6192 11.0451 14.4084L11.0757 14.3955V14.3623V13.6152V13.541L11.007 13.5688C10.4778 13.7828 9.906 13.9574 9.29152 14.0927C8.67809 14.2278 8.0591 14.2953 7.43448 14.2953C6.25243 14.2953 5.24815 14.0618 4.41951 13.5971C3.59082 13.1323 2.95818 12.4635 2.52104 11.5892C2.08378 10.7147 1.8641 9.6671 1.8641 8.44478C1.8641 7.4082 2.01621 6.4832 2.31942 5.66903C2.6228 4.84879 3.05781 4.15308 3.62404 3.58124C4.19043 3.00363 4.8693 2.56301 5.66097 2.25982C6.45249 1.95669 7.34058 1.80467 8.32593 1.80467C9.46273 1.80467 10.4361 2.02694 11.248 2.46929C12.06 2.9061 12.6814 3.53016 13.1126 4.3421C13.5494 5.15419 13.769 6.11664 13.769 7.23072C13.769 7.91194 13.6929 8.52137 13.5416 9.05951C13.396 9.59706 13.1757 10.0196 12.8828 10.329C12.5929 10.6298 12.2271 10.7805 11.7814 10.7805C11.3518 10.7805 11.0403 10.6408 10.8368 10.3678C10.631 10.0862 10.5239 9.64344 10.5239 9.03059C10.5239 8.83362 10.5295 8.62811 10.5408 8.41404C10.5578 8.19293 10.5691 7.97741 10.5748 7.76748C10.5748 7.76766 10.5748 7.7673 10.5748 7.76748L10.7191 4.86449L10.721 4.827L10.6855 4.81475C10.371 4.7061 10.006 4.61779 9.59089 4.54953C9.17469 4.47544 8.73887 4.43845 8.28348 4.43845C7.53427 4.43845 6.87702 4.60725 6.31348 4.94652C5.75659 5.28525 5.32007 5.75054 5.00468 6.34118C4.69478 6.93229 4.54033 7.60868 4.54033 8.36838C4.54033 9.36084 4.77707 10.1435 5.25684 10.71C5.73801 11.2723 6.42123 11.5512 7.29864 11.5512C7.87898 11.5512 8.40065 11.4073 8.86196 11.119Z" fill="#8C8C8C"/>
<path d="M9.04202 10.0442C9.42943 9.54613 9.65162 8.81342 9.7023 7.83956M9.04202 10.0442L9.7023 7.83956M9.04202 10.0442C8.66257 10.5337 8.11239 10.7805 7.38354 10.7805C6.74417 10.7805 6.26469 10.5782 5.93665 10.1792C5.61269 9.77273 5.44688 9.17449 5.44688 8.37687C5.44688 7.76611 5.55892 7.22455 5.78172 6.7511C6.00428 6.27816 6.32639 5.90628 6.74832 5.63423C7.17471 5.36293 7.6885 5.22614 8.29197 5.22614C8.60117 5.22614 8.88734 5.24582 9.15058 5.28502C9.3992 5.32205 9.62203 5.37393 9.81933 5.44043L9.7023 7.83956M10.5748 7.76748C10.5691 7.97741 10.5578 8.19293 10.5408 8.41404C10.5295 8.62811 10.5239 8.83362 10.5239 9.03059C10.5239 9.64344 10.631 10.0862 10.8368 10.3678C11.0403 10.6408 11.3518 10.7805 11.7814 10.7805C12.2271 10.7805 12.5929 10.6298 12.8828 10.329C13.1757 10.0196 13.396 9.59706 13.5416 9.05951C13.6929 8.52137 13.769 7.91194 13.769 7.23072C13.769 6.11664 13.5494 5.15419 13.1126 4.3421C12.6814 3.53016 12.06 2.9061 11.248 2.46929C10.4361 2.02694 9.46273 1.80467 8.32593 1.80467C7.34058 1.80467 6.45249 1.95669 5.66097 2.25982C4.8693 2.56301 4.19043 3.00363 3.62404 3.58124C3.05781 4.15308 2.6228 4.84879 2.31942 5.66903C2.01621 6.4832 1.8641 7.4082 1.8641 8.44478C1.8641 9.6671 2.08378 10.7147 2.52104 11.5892C2.95818 12.4635 3.59082 13.1323 4.41951 13.5971C5.24815 14.0618 6.25243 14.2953 7.43448 14.2953C8.0591 14.2953 8.67809 14.2278 9.29152 14.0927C9.906 13.9574 10.4778 13.7828 11.007 13.5688L11.0757 13.541V13.6152V14.3623V14.3955L11.0451 14.4084C10.5437 14.6192 9.99155 14.7871 9.38885 14.9122C8.78513 15.0375 8.13362 15.1 7.43448 15.1C6.10331 15.1 4.95398 14.8326 3.98859 14.2956C3.02906 13.7587 2.28916 12.9929 1.76969 11.9996C1.25588 11.0062 1 9.82638 1 8.46177C1 7.31346 1.17338 6.28006 1.5211 5.36228C1.86864 4.4393 2.36474 3.65487 3.00972 3.00987C3.6545 2.3594 4.42465 1.86321 5.31952 1.52121C6.22029 1.17346 7.22265 1 8.32593 1C9.59482 1 10.7013 1.25325 11.6438 1.7616C12.586 2.26984 13.3174 2.98979 13.8371 3.92071C14.3571 4.8521 14.6161 5.95605 14.6161 7.23072C14.6161 7.80569 14.5591 8.35266 14.4451 8.87153C14.3309 9.39102 14.1537 9.85175 13.9129 10.2531C13.6773 10.6554 13.3777 10.9727 13.0139 11.2037C12.6538 11.436 12.2281 11.5512 11.7389 11.5512C11.2077 11.5512 10.7697 11.3943 10.4307 11.0787C10.1218 10.7807 9.9425 10.3905 9.8905 9.91177C9.64591 10.4379 9.3033 10.8411 8.86196 11.119C8.40065 11.4073 7.87898 11.5512 7.29864 11.5512C6.42123 11.5512 5.73801 11.2723 5.25684 10.71C4.77707 10.1435 4.54033 9.36084 4.54033 8.36838C4.54033 7.60868 4.69478 6.93229 5.00468 6.34118C5.32007 5.75054 5.75659 5.28525 6.31348 4.94652C6.87702 4.60725 7.53427 4.43845 8.28348 4.43845C8.73887 4.43845 9.17469 4.47544 9.59089 4.54953C10.006 4.61779 10.371 4.7061 10.6855 4.81475L10.721 4.827L10.7191 4.86449L10.5748 7.76748ZM10.5748 7.76748C10.5748 7.7673 10.5748 7.76766 10.5748 7.76748Z" stroke="#8C8C8C" strokeWidth="0.1"/>
</svg>

  );
}

export default AtIcon;
