# ---------------------------------------------
# 🌐 Frontend Configuration
# ---------------------------------------------
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:4050/napi
NEXT_PUBLIC_SERVER_NODE_API_URL=http://node_app:4050/napi
NEXT_PUBLIC_PYTHON_API_URL=http://localhost:9089/pyapi
NEXT_PUBLIC_DOMAIN_URL=http://localhost:3000
NEXT_PUBLIC_API_PREFIX=/api
NEXT_PUBLIC_APP_ENVIRONMENT=development

# ---------------------------------------------
# 🔐 Authentication
# ---------------------------------------------
NEXT_PUBLIC_COOKIE_NAME=cookie
NEXT_PUBLIC_COOKIE_PASSWORD=your_cookie_password_here
NEXT_PUBLIC_AWS_S3_URL=http://localhost:9000/weam-frontend-media
NEXT_PUBLIC_HTTPS_PROTOCOL=http
NEXT_PUBLIC_IMAGE_DOMAIN=localhost:3000

# ---------------------------------------------
# 🔌 Socket Configuration
# ---------------------------------------------
NEXT_PUBLIC_SOCKET_CONNECTION_URL=http://localhost:4050
NEXT_PUBLIC_SECURITY_KEY=your_security_key_here
NEXT_PUBLIC_MESSAGE_LIMIT=100
NEXT_PUBLIC_FREE_TRIAL_DAYS=30
NEXT_PUBLIC_OPENAI_PLATFORM_URL=https://platform.openai.com/settings/organization/billing/overview
NEXT_PUBLIC_FRESHDESK_SUPPORT_URL=https://your-support-url.com
CSRF_TOKEN_SECRET=your_csrf_token_secret_here

# ---------------------------------------------
# ⚙️ Node.js Server Configuration
# ---------------------------------------------
SERVER_PORT=4050
API_PREFIX=v1
LOCAL_LOG=false

# ---------------------------------------------
# 🤖 LLM Model API URLs
# ---------------------------------------------
FRONT_URL=http://localhost:3000
OPEN_AI_MODAL=https://api.openai.com/v1/models
OPEN_AI_API_URL=https://api.openai.com
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta/models
ANTHROPIC_AI_API_URL=https://api.anthropic.com/v1
PYTHON_API_URL=http://gateway_service:9089/pyapi

# ---------------------------------------------
# 🔐 JWT Authentication
# ---------------------------------------------
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_ACCESS_EXPIRE=24h
JWT_REFRESH_EXPIRE=30d
QR_NAME=weam
SECURITY_KEY=your_security_key_here
JWT_ALGORITHM=HS256

# ---------------------------------------------
# 🧠 Redis Configuration
# ---------------------------------------------
REDIS_HOST=redis
REDIS_PORT=6379

# ---------------------------------------------
# 🌍 Environment Configuration:- (options: enterprise, local, dev, prod)
# ---------------------------------------------
WEAM_ENVIRONMENT=enterprise
STACK_NAME=EnterpriseStack
ENV_FILE=.env

# ---------------------------------------------
# 🚪 Gateway Service
# ---------------------------------------------
GATEWAY_PORT=9089
ENVIRONMENT_URL=http://localhost:3000

# ---------------------------------------------
# 🎯 Celery Task Queue
# ---------------------------------------------
CELERY_BROKEN_URL=redis://redis:6379/7
CELERY_RESULT_BACKEND=redis://redis:6379/7
DELETE_TASK_ON_SUCCESS=true

# ---------------------------------------------
# ☁️ MinIO Storage Configuration
# ---------------------------------------------
BUCKET_TYPE=MINIO
MINIO_PORT=9000
MINIO_DASHBOARD_PORT=9001
AWS_ACCESS_KEY_ID=your_minio_access_key
AWS_SECRET_ACCESS_KEY=your_minio_secret_key
AWS_S3_API_VERSION=2012-10-17
AWS_BUCKET=weam-frontend-media
MINIO_ENDPOINT=http://minio:9000
MINIO_REGEX_FILE_PATTERN=^https?://(?:localhost|minio):9000/
PROFILER_S3_BUCKET=sample-profiler
AWS_VECTORS_BACKUP=sample-vectors-backup-bucket

# ---------------------------------------------
# ✉️ Email (SMTP) Configuration
# ---------------------------------------------
EMAIL_PROVIDER=SMTP
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_PORT=587
SMTP_SERVER=smtp.example.com
SENDER_EMAIL="Weam <<EMAIL>>"
SUPPORT_EMAIL=<EMAIL>
API_RATE_LIMIT=10
SEED=1
TZ=Asia/Kolkata
FRESHDESK_SUPPORT_URL=https://your-support-url.com
FREE_TIER_CREDIT=10

# ---------------------------------------------
# 🍃 MongoDB Configuration
# ---------------------------------------------
MONOGODB_URI=**************************************************************************************************
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password
DB_DATABASE=your_database_name
MONGO_PORT=27017

# ---------------------------------------------
# 🧲 Qdrant Vector DB
# ---------------------------------------------
LOCAL_QDRANT_URL=http://qdrant_primary_container:6333
QDRANT_DASHBOARD_PORT=6333
QDRANT_NODE_PORT=6335

# ---------------------------------------------
# 🐞 WDB Debugger
# ---------------------------------------------
WDB_SOCKET_SERVER=wdb
WDB_NO_BROWSER_AUTO_OPEN=1
WDB_PORT=1984

# ---------------------------------------------
# 📈 SEO Agent Configuration
# ---------------------------------------------
SEO_USER_ID=your_seo_user_id
SEO_PASSWORD=your_seo_password
