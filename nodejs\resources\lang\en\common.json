{"module": {"create": "{module} has been created successfully.", "update": "{module} has been updated successfully.", "delete": "{module} has been deleted successfully.", "archive": "{module} has been archived successfully.", "get": "{module} has been retrieve successfully.", "list": "All {module} has been obtained successfully.", "createError": "Error accured while creating the {module}.", "updateError": "Error accured while updating the {module}.", "getError": "Error accured while getting the {module}.", "deleteError": "Error accured while deleting the {module}.", "listError": "{module} not found or you can't get all {module}.", "notFound": "{module} not found.", "alreadyExists": "{module} already exist.", "active": "{module} has been activated successfully.", "deActive": "{module} has been deActivated successfully.", "tokenUpdated": "Device token updated.", "success": "Your {module} has been accepted successfully", "failed": "{module} failed.", "prompt_expire": "Your free trial is over! Get unlimited access by upgrading today. Thanks for choosing <PERSON><PERSON>!", "cancel": "Your {module} has been canceled successfully", "cancelError": "Error accured while canceling {module} please try again.", "share": "{module} added successfully", "shareError": "Something went wrong during {module} share.", "unshare": "{module} removed successfully", "unshareError": "Something went wrong during {module} unshare.", "add": "{module} added to {module2}", "remove": "{module} remove from {module2}", "restore": "{module} has been restored successfully.", "storageRequest": "Your storage request is successfully sent to your administrator", "alreadyTaken": "{module} is already taken.", "toggle": "{module} successfully", "unAuthorized": "You're unAuthorized to access this {module}", "storageRequestAccept": "Storage request accepted successfully", "storageRequestDecline": "Storage request declined successfully", "storageRequestUpdateError": "Error occured while update storage request", "storageRequestExist": "You already have a pending storage request", "storageRequestCharge": "Storage request charged successfully. Storage will be updated to requested user account soon.", "invalid": "Invalid Request", "favorite": "{module} added to Favourites", "favoriteError": "Error occured while favoriting {module}", "unfavorite": "{module} removed from Favourites", "unfavoriteError": "Error occured while unfavoriting {module}", "credit_expired": "Your credit limit has been reached. Please contact your administrator to increase your credit limit.", "trial_expired": "Your trial period has expired. Please upgrade to a paid plan to continue using our services."}, "ai": {"open_ai_billing_error": "You exceeded your current quota, please check your plan and billing details.", "api_config_success": "API key has been successfully configured!"}, "subscription": {"noSubscription": "No active subscription found for this company", "userLimitExceeded": "The number of users you've entered exceeds the limit for this plan. Please choose a higher tier plan or reduce the number of users", "inviteUserLimitExceed": "User limit reached. Increase your subscription limit to invite more users.", "inviteLinkUserLimitExceed": "The user limit of your plan has been reached, please contact your administrator.", "create": "Subscription created successfully", "createError": "Error occured while creating subscription", "update": "Subscription updated successfully", "updateError": "Error occured while updating subscription", "cancel": "Subscription cancelled successfully", "cancelError": "Error occured while cancelling subscription", "alreadyCancel": "Your subscription already cancelled", "noActiveSubscription": "No active subscription found for this company", "alreadyExists": "Subscription already exists for this company", "invalidCoupon": "Invalid coupon", "paymentMethodUpdate": "Card details updated successfully", "paymentMethodUpdateError": "Error occured while updating card details", "defaultPaymentMethod": "Default payment method retrieved successfully", "unAuthorized": "You're unAuthorized to access this {module}", "checkCoupon": "Coupon code applied successfully", "checkCouponError": "Error occured while validating coupon code", "paymentFailed": "Payment was not successfully completed", "uncancel": "Subscription activated successfully"}}