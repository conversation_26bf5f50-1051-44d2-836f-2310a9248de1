import React from 'react';

const ShareUser = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.3999 4.71144C2.3999 3.65058 2.82133 2.63316 3.57148 1.88301C4.32162 1.13287 5.33904 0.711441 6.3999 0.711441C7.46077 0.711441 8.47818 1.13287 9.22833 1.88301C9.97848 2.63316 10.3999 3.65058 10.3999 4.71144C10.3999 5.77231 9.97848 6.78972 9.22833 7.53987C8.47818 8.29001 7.46077 8.71144 6.3999 8.71144C5.33904 8.71144 4.32162 8.29001 3.57148 7.53987C2.82133 6.78972 2.3999 5.77231 2.3999 4.71144ZM6.3999 2.31144C6.08473 2.31144 5.77264 2.37352 5.48146 2.49413C5.19028 2.61474 4.92571 2.79152 4.70285 3.01438C4.47999 3.23725 4.3032 3.50182 4.18259 3.793C4.06198 4.08418 3.9999 4.39627 3.9999 4.71144C3.9999 5.02661 4.06198 5.3387 4.18259 5.62988C4.3032 5.92106 4.47999 6.18564 4.70285 6.4085C4.92571 6.63136 5.19028 6.80814 5.48146 6.92875C5.77264 7.04936 6.08473 7.11144 6.3999 7.11144C7.03642 7.11144 7.64687 6.85859 8.09696 6.4085C8.54705 5.95841 8.7999 5.34796 8.7999 4.71144C8.7999 4.07492 8.54705 3.46447 8.09696 3.01438C7.64687 2.5643 7.03642 2.31144 6.3999 2.31144Z"/>
        <path d="M4 11.9114C3.36348 11.9114 2.75303 12.1643 2.30294 12.6144C1.85286 13.0645 1.6 13.6749 1.6 14.3114V16.7114H0V14.3114C0 13.2506 0.421427 12.2332 1.17157 11.483C1.92172 10.7329 2.93913 10.3114 4 10.3114H8.8C9.86087 10.3114 10.8783 10.7329 11.6284 11.483C12.3786 12.2332 12.8 13.2506 12.8 14.3114V16.7114H11.2V14.3114C11.2 13.6749 10.9471 13.0645 10.4971 12.6144C10.047 12.1643 9.43652 11.9114 8.8 11.9114H4ZM15.2 7.11144V4.71144H13.6V7.11144H11.2V8.71144H13.6V11.1114H15.2V8.71144H17.6V7.11144H15.2Z"/>
    </svg>
  );
}

export default ShareUser;
