const GitHubIcon = ({ className }) => {
    return (
        <svg
            width="28"
            height="27"
            className={className}
            viewBox="0 0 28 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M13.9983 0C6.2685 0 0 6.19726 0 13.8421C0 19.9567 4.011 25.144 9.576 26.976C10.276 27.1035 10.5315 26.6761 10.5315 26.309C10.5315 25.9799 10.5192 25.1096 10.5122 23.9549C6.6185 24.7908 5.796 22.0988 5.796 22.0988C5.16075 20.4995 4.242 20.0739 4.242 20.0739C2.96975 19.2156 4.3365 19.2329 4.3365 19.2329C5.74175 19.3311 6.48025 20.6598 6.48025 20.6598C7.72975 22.7744 9.758 22.1643 10.556 21.8093C10.682 20.9149 11.0442 20.3048 11.445 19.9584C8.337 19.6086 5.068 18.4212 5.068 13.1183C5.068 11.6069 5.614 10.3713 6.51 9.40274C6.36475 9.05289 5.88525 7.64489 6.6465 5.74056C6.6465 5.74056 7.8225 5.36831 10.4965 7.1589C11.613 6.85214 12.81 6.69876 14.0018 6.69359C15.19 6.70048 16.3888 6.85214 17.507 7.16062C20.1792 5.37004 21.3535 5.74229 21.3535 5.74229C22.1165 7.64834 21.637 9.05461 21.4935 9.40446C22.3912 10.373 22.932 11.6087 22.932 13.1201C22.932 18.4367 19.6595 19.6068 16.541 19.9498C17.0432 20.3772 17.4912 21.2216 17.4912 22.5125C17.4912 24.3634 17.4737 25.8558 17.4737 26.309C17.4737 26.6796 17.7257 27.1104 18.4362 26.9743C23.9925 25.1406 28 19.955 28 13.8421C28 6.19726 21.7315 0 13.9983 0Z"
                fill="black"
            />
        </svg>
    );
};

export default GitHubIcon;
