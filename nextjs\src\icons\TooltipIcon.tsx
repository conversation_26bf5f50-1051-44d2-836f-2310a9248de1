import React from 'react';

const TooltipIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M9 0C4.03714 0 0 4.03714 0 9C0 10.4448 0.348107 11.8671 1.00929 13.1394L0.0331072 16.7863C-0.0097106 16.9462 -0.0106404 17.1144 0.0304074 17.2748C0.0714551 17.4352 0.153098 17.5823 0.267476 17.702C0.381854 17.8216 0.525114 17.9098 0.683466 17.9581C0.841817 18.0064 1.00993 18.013 1.17161 17.9775L5.10429 17.1129C6.31998 17.6968 7.65136 17.9999 9 18C13.9629 18 18 13.9629 18 9C18 4.03714 13.9629 0 9 0ZM9 16.0714C7.84286 16.0714 6.69696 15.7863 5.68736 15.2466C5.48477 15.1384 5.25019 15.106 5.02586 15.1554L2.30593 15.7532L2.97257 13.2641C3.00543 13.1414 3.01374 13.0135 2.99703 12.8876C2.98032 12.7617 2.93891 12.6404 2.87518 12.5306C2.25427 11.4576 1.92772 10.2397 1.92857 9C1.92857 5.10107 5.10107 1.92857 9 1.92857C12.8989 1.92857 16.0714 5.10107 16.0714 9C16.0714 12.8989 12.8989 16.0714 9 16.0714ZM9.96429 9.20089V12.4152C9.96429 12.6709 9.86269 12.9162 9.68185 13.097C9.50101 13.2779 9.25574 13.3795 9 13.3795C8.74426 13.3795 8.49899 13.2779 8.31815 13.097C8.13731 12.9162 8.03571 12.6709 8.03571 12.4152V9.20089C8.03571 8.94515 8.13731 8.69988 8.31815 8.51904C8.49899 8.3382 8.74426 8.23661 9 8.23661C9.25574 8.23661 9.50101 8.3382 9.68185 8.51904C9.86269 8.69988 9.96429 8.94515 9.96429 9.20089ZM9.68143 5.18464C9.86143 5.36464 9.96429 5.61214 9.96429 5.86607C9.96429 6.12 9.86143 6.3675 9.68143 6.5475C9.50143 6.7275 9.25393 6.83036 9 6.83036C8.74607 6.83036 8.49857 6.7275 8.31857 6.5475C8.13857 6.3675 8.03571 6.12 8.03571 5.86607C8.03571 5.61214 8.13857 5.36464 8.31857 5.18464C8.67857 4.82464 9.32143 4.82464 9.68143 5.18464Z" />
        </svg>
    );
};

export default TooltipIcon;
