const LockIcon = ({height, width, className}:any) => {
    return (
        <svg className={className} width={width} height={height}
            viewBox="0 0 14 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.6875 5.0625C1.6875 2.25 3.9375 0 6.75 0C9.5625 0 11.8125 2.25 11.8125 5.0625V7.3125C12.7688 7.3125 13.5 8.04375 13.5 9V16.3125C13.5 17.2687 12.7688 18 11.8125 18H1.6875C0.73125 18 0 17.2687 0 16.3125V9C0 8.04375 0.73125 7.3125 1.6875 7.3125V5.0625ZM12.375 16.3125V9C12.375 8.6625 12.15 8.4375 11.8125 8.4375H1.6875C1.35 8.4375 1.125 8.6625 1.125 9V16.3125C1.125 16.65 1.35 16.875 1.6875 16.875H11.8125C12.15 16.875 12.375 16.65 12.375 16.3125ZM2.8125 5.0625V7.3125H10.6875V5.0625C10.6875 2.86875 8.94375 1.125 6.75 1.125C4.55625 1.125 2.8125 2.86875 2.8125 5.0625ZM5 11.6875C5 10.7312 5.73125 10 6.6875 10C7.64375 10 8.375 10.7312 8.375 11.6875C8.375 12.4188 7.925 13.0375 7.25 13.2625V14.5C7.25 14.8375 7.025 15.0625 6.6875 15.0625C6.35 15.0625 6.125 14.8375 6.125 14.5V13.2625C5.45 13.0375 5 12.4188 5 11.6875ZM6.125 11.6875C6.125 12.025 6.35 12.25 6.6875 12.25C7.025 12.25 7.25 12.025 7.25 11.6875C7.25 11.35 7.025 11.125 6.6875 11.125C6.35 11.125 6.125 11.35 6.125 11.6875Z"
            />
        </svg>
    );
};


export default LockIcon;