import React from 'react';

const ReportIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path fillRule="evenodd" clipRule="evenodd" d="M11 8.41348C8.4595 8.41348 6.40663 10.4706 6.40663 13C6.40663 15.5294 8.4595 17.5865 11 17.5865C13.5405 17.5865 15.5934 15.5294 15.5934 13C15.5934 10.4706 13.5405 8.41348 11 8.41348ZM5 13C5 9.68263 7.68995 7 11 7C14.3101 7 17 9.68263 17 13C17 16.3174 14.3101 19 11 19C7.68995 19 5 16.3174 5 13Z" />
            <path fillRule="evenodd" clipRule="evenodd" d="M10.9887 3.38983C5.67905 3.38983 1.38127 7.696 1.38127 13C1.38127 18.304 5.67906 22.6102 10.9887 22.6102C13.7672 22.6102 16.0676 21.6205 17.8077 19.7701C18.0699 19.4913 18.507 19.4792 18.784 19.743C19.0611 20.0067 19.0732 20.4466 18.811 20.7253C16.7948 22.8693 14.124 24 10.9887 24C4.92338 24 0 19.0787 0 13C0 6.92121 4.92338 2 10.9887 2C11.3702 2 11.6794 2.31112 11.6794 2.69492C11.6794 3.07871 11.3702 3.38983 10.9887 3.38983Z" />
            <path fillRule="evenodd" clipRule="evenodd" d="M10.4843 0.691007C10.4843 0.309375 10.7991 0 11.1875 0C18.2168 0 24 5.65467 24 12.6293C24 16.0744 22.5882 19.1978 20.302 21.4752C20.1707 21.606 19.9918 21.68 19.8048 21.6809C19.6178 21.6818 19.4382 21.6094 19.3056 21.4799L15 17C14.7247 16.7309 14.7234 16.2934 14.9973 16.0228C15.2711 15.7522 15.7163 15.751 15.9916 16.0201L19.7814 19.9958C21.5343 18.0215 22.5938 15.4466 22.5938 12.6293C22.5938 6.65182 17.8214 1.76385 11.8906 1.40333V7.47283C11.8906 7.85446 11.5758 8.16384 11.1875 8.16384C10.7991 8.16384 10.4843 7.85446 10.4843 7.47283V0.691007ZM0 12.6482C0 12.2666 0.3148 11.9572 0.703125 11.9572L5.29688 11.9572C5.6852 11.9572 6 12.2666 6 12.6482C6 13.0299 5.6852 13.3393 5.29688 13.3393L0.703125 13.3393C0.3148 13.3393 0 13.0299 0 12.6482ZM9.60851 17.6017C9.96903 17.7435 10.1443 18.1457 10 18.5L8.33554 22.5656C8.19124 22.9199 7.782 23.0921 7.42148 22.9503C7.06096 22.8085 6.88569 22.4063 7.02999 22.052L8.69445 17.9864C8.83876 17.6321 9.248 17.4599 9.60851 17.6017Z" />
        </svg>
    );
};

export default ReportIcon;
