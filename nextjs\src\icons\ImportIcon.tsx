import React from 'react';

const ImportIcon = ({ width, height, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 159 137"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36.8938 0C32.7019 0.0261153 28.6894 1.70428 25.7271 4.67032C22.7648 7.63635 21.0917 11.6509 21.0708 15.8429V21.0708H15.8429C11.6475 21.0917 7.62995 22.7676 4.66336 25.7342C1.69676 28.7008 0.0208988 32.7183 0 36.9137V121.157C0 129.824 7.15612 137 15.8429 137H142.248C146.434 136.969 150.44 135.288 153.395 132.321C156.349 129.355 158.015 125.344 158.031 121.157V68.5199C158.094 67.7923 158.006 67.0596 157.771 66.3681C157.536 65.6766 157.16 65.0415 156.666 64.5032C156.173 63.9648 155.573 63.535 154.904 63.2409C154.236 62.9468 153.514 62.795 152.783 62.795C152.053 62.795 151.331 62.9468 150.662 63.2409C149.994 63.535 149.394 63.9648 148.9 64.5032C148.407 65.0415 148.03 65.6766 147.796 66.3681C147.561 67.0596 147.472 67.7923 147.535 68.5199V121.157C147.554 121.858 147.431 122.555 147.172 123.206C146.913 123.857 146.525 124.449 146.03 124.945C145.536 125.442 144.945 125.833 144.295 126.094C143.645 126.355 142.948 126.481 142.248 126.465H15.823C15.1224 126.481 14.4259 126.355 13.7756 126.094C13.1253 125.833 12.535 125.442 12.0404 124.945C11.5458 124.449 11.1574 123.857 10.8987 123.206C10.6399 122.555 10.5163 121.858 10.5354 121.157V36.9137C10.5162 36.2115 10.6404 35.5127 10.9003 34.8601C11.1603 34.2075 11.5505 33.6148 12.0472 33.118C12.544 32.6213 13.1367 32.2311 13.7893 31.9711C14.4419 31.7112 15.1407 31.587 15.8429 31.6062H142.248C142.948 31.5898 143.645 31.716 144.295 31.9771C144.945 32.2383 145.536 32.629 146.03 33.1254C146.525 33.6218 146.913 34.2137 147.172 34.8649C147.431 35.5161 147.554 36.2132 147.535 36.9137V47.4491C147.472 48.1766 147.561 48.9094 147.796 49.6009C148.03 50.2924 148.407 50.9274 148.9 51.4658C149.394 52.0041 149.994 52.434 150.662 52.7281C151.331 53.0221 152.053 53.174 152.783 53.174C153.514 53.174 154.236 53.0221 154.904 52.7281C155.573 52.434 156.173 52.0041 156.666 51.4658C157.16 50.9274 157.536 50.2924 157.771 49.6009C158.006 48.9094 158.094 48.1766 158.031 47.4491V36.9137C158.015 32.727 156.349 28.7153 153.395 25.7493C150.44 22.7833 146.434 21.1022 142.248 21.0708H137V15.8429C136.974 11.6491 135.296 7.63455 132.331 4.66907C129.365 1.70359 125.351 0.026065 121.157 0H36.8938ZM36.8938 10.5354H121.177C124.159 10.5354 126.465 12.8214 126.465 15.8429V21.0708H31.6062V15.8429C31.6062 12.8214 33.8922 10.5354 36.9137 10.5354H36.8938ZM78.936 42.082C78.2442 42.0949 77.5617 42.244 76.9276 42.5207C76.2934 42.7975 75.72 43.1965 75.24 43.6949C74.7601 44.1933 74.383 44.7813 74.1304 45.4255C73.8778 46.0697 73.7545 46.7573 73.7677 47.4491V97.9395L61.6818 85.8536C61.1341 85.2853 60.4657 84.8473 59.7259 84.572C58.9862 84.2968 58.194 84.1913 57.408 84.2633C56.4072 84.3601 55.4549 84.7413 54.6637 85.3618C53.8725 85.9824 53.2755 86.8165 52.9432 87.7655C52.6108 88.7145 52.557 89.7388 52.7881 90.7174C53.0192 91.696 53.5256 92.588 54.2474 93.288L75.3182 114.359C76.3055 115.342 77.6421 115.894 79.0354 115.894C80.4288 115.894 81.7654 115.342 82.7526 114.359L103.823 93.288C104.38 92.8179 104.832 92.2375 105.153 91.5836C105.473 90.9297 105.655 90.2164 105.685 89.4888C105.716 88.7613 105.596 88.0352 105.332 87.3565C105.068 86.6777 104.667 86.0611 104.152 85.5454C103.638 85.0298 103.022 84.6264 102.344 84.3607C101.666 84.095 100.941 83.9728 100.213 84.0017C99.4854 84.0307 98.7716 84.2102 98.1169 84.529C97.4621 84.8477 96.8805 85.2987 96.4089 85.8536L84.3031 97.9196V47.4292C84.3166 46.7207 84.187 46.0167 83.9221 45.3594C83.6571 44.7022 83.2623 44.1051 82.7612 43.604C82.2601 43.1029 81.663 42.7081 81.0058 42.4431C80.3485 42.1782 79.6445 42.0685 78.936 42.082Z"
            />
        </svg>
    );
};

export default ImportIcon;