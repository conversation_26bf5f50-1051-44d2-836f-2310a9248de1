import { SvgProps } from '@/types/assets';
import React from 'react';

const ChatIcon = ({ width, height, className, fill }:SvgProps) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 16 16"
            fill={fill}
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M12.3636 0H3.63634C1.63126 0 0 1.63126 0 3.63634V9.45448C0.00136105 10.2923 0.291402 11.104 0.821273 11.753C1.35114 12.4019 2.08847 12.8485 2.90907 13.0174V15.2726C2.90905 15.4043 2.94478 15.5335 3.01243 15.6464C3.08009 15.7594 3.17715 15.8519 3.29324 15.914C3.40933 15.9761 3.54011 16.0055 3.67162 15.9991C3.80313 15.9928 3.93045 15.9508 4.03997 15.8777L8.22031 13.0908H12.3636C14.3686 13.0908 15.9999 11.4596 15.9999 9.45448V3.63634C15.9999 1.63126 14.3686 0 12.3636 0ZM7.59631 11.7585L4.36361 13.9134V12.3636C4.36361 12.1707 4.28698 11.9857 4.15059 11.8493C4.01421 11.7129 3.82922 11.6363 3.63634 11.6363C2.43344 11.6363 1.45454 10.6574 1.45454 9.45448V3.63634C1.45454 2.43344 2.43344 1.45454 3.63634 1.45454H12.3636C13.5665 1.45454 14.5454 2.43344 14.5454 3.63634V9.45448C14.5454 10.6574 13.5665 11.6363 12.3636 11.6363H7.99995C7.85596 11.634 7.71486 11.6767 7.59631 11.7585Z" />
            <path d="M12.3635 4.36353H3.63633C3.44344 4.36353 3.25846 4.44015 3.12207 4.57654C2.98568 4.71293 2.90906 4.89791 2.90906 5.09079C2.90906 5.28368 2.98568 5.46866 3.12207 5.60505C3.25846 5.74144 3.44344 5.81806 3.63633 5.81806H12.3635C12.5564 5.81806 12.7414 5.74144 12.8778 5.60505C13.0142 5.46866 13.0908 5.28368 13.0908 5.09079C13.0908 4.89791 13.0142 4.71293 12.8778 4.57654C12.7414 4.44015 12.5564 4.36353 12.3635 4.36353ZM10.909 7.2726H5.09086C4.89798 7.2726 4.71299 7.34922 4.57661 7.48561C4.44022 7.622 4.36359 7.80698 4.36359 7.99986C4.36359 8.19275 4.44022 8.37773 4.57661 8.51412C4.71299 8.65051 4.89798 8.72713 5.09086 8.72713H10.909C11.1019 8.72713 11.2869 8.65051 11.4233 8.51412C11.5596 8.37773 11.6363 8.19275 11.6363 7.99986C11.6363 7.80698 11.5596 7.622 11.4233 7.48561C11.2869 7.34922 11.1019 7.2726 10.909 7.2726Z" />
        </svg>
    );
};

export default ChatIcon;