const TickIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 505 505"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M430.884 73.928C383.211 26.255 319.826 0 252.406 0C184.986 0 121.601 26.255 73.928 73.928C26.254 121.601 0 184.986 0 252.406C0 319.826 26.254 383.211 73.928 430.884C121.601 478.557 184.986 504.812 252.406 504.812C319.826 504.812 383.211 478.557 430.884 430.884C478.557 383.211 504.812 319.826 504.812 252.406C504.812 184.986 478.558 121.601 430.884 73.928ZM252.406 454.153C141.162 454.153 50.659 363.649 50.659 252.406C50.659 141.163 141.162 50.659 252.406 50.659C363.65 50.659 454.153 141.163 454.153 252.406C454.153 363.649 363.649 454.153 252.406 454.153Z"
            />
            <path
                d="M393.291 155.145C383.527 145.382 367.699 145.382 357.936 155.145L209.55 303.531L136.199 230.181C126.435 220.418 110.607 220.418 100.844 230.181C91.0807 239.944 91.0807 255.773 100.844 265.536L191.872 356.564C196.754 361.445 203.151 363.886 209.55 363.886C215.949 363.886 222.346 361.445 227.228 356.564L393.291 190.5C403.054 180.737 403.054 164.908 393.291 155.145Z"
            />
        </svg>
    );
};

export default TickIcon;
