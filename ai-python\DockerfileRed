# Use an official lightweight Python image as a parent image
FROM pybase_image:latest

# Copy the current directory contents into the container at /app
# Set the working directory in the container to /app


COPY celery_entrypoint.sh  /usr/local/bin/
# # Make the entrypoint scripts executable
RUN chmod +x /usr/local/bin/celery_entrypoint.sh

# Set PYTHONPATH
# ENV PYTHONPATH=/app

WORKDIR /app/ai-python

# Copy the current directory contents into the container at /app
COPY . /app


# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1


