import React from 'react';

const Down<PERSON>ong<PERSON>rrow = ({ height, width, className }) => {
  return (
    <svg width={width} height={height} className={className} viewBox="0 0 11 13" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.27434 1.37056C4.2666 1.41827 4.26302 1.46655 4.26365 1.51487L4.26365 9.74103L4.18022 9.56165C4.09723 9.38632 3.9843 9.22681 3.8465 9.09028L1.53968 6.78346C1.24966 6.47964 0.783109 6.4286 0.434244 6.66248C0.0624881 6.9347 -0.0182297 7.45673 0.253984 7.82851C0.275987 7.85855 0.299974 7.88709 0.325786 7.91392L4.49725 12.0854C4.82289 12.4114 5.35112 12.4117 5.67712 12.086L5.67778 12.0854L9.84924 7.91392C10.1746 7.58766 10.1739 7.05943 9.8476 6.73405C9.82192 6.70845 9.7946 6.68454 9.76581 6.66248C9.41695 6.4286 8.95039 6.47964 8.66037 6.78346L6.34938 9.08611C6.22684 9.20851 6.12411 9.34924 6.04486 9.50325L5.93223 9.75354L5.93223 1.56078C5.94806 1.13459 5.65035 0.76067 5.23143 0.680604C4.77661 0.606847 4.34812 0.915745 4.27434 1.37056Z"/>
    </svg>
  );
}

export default DownLongArrow;
