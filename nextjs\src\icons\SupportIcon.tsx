import React from 'react';

const SupportIcon = ({height, width, className}:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 140 140"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M12.5624 139.993C10.5749 139.984 8.61789 139.505 6.85087 138.595C5.08385 137.685 3.55691 136.37 2.39458 134.758C1.23226 133.146 0.467463 131.282 0.162576 129.318C-0.14231 127.354 0.0213485 125.346 0.640203 123.457L6.66578 105.545C7.37466 103.544 7.30561 101.35 6.47233 99.3978C-0.430716 84.4805 -1.86415 67.6098 2.42319 51.7417C6.71054 35.8736 16.4452 22.0203 29.9214 12.6093C43.3977 3.19819 59.7559 -1.17038 76.1299 0.26902C92.5038 1.70842 107.849 8.86397 119.477 20.4819C131.104 32.0997 138.273 47.4389 139.726 63.8116C141.179 80.1844 136.824 96.5463 127.424 110.03C118.025 123.515 104.179 133.261 88.315 137.561C72.4505 141.862 55.5786 140.443 40.6555 133.552C38.6655 132.695 36.4237 132.629 34.3863 133.366L16.5532 139.356C15.2656 139.78 13.9182 139.996 12.5624 139.993ZM69.8807 11.0274C59.9644 11.0073 50.203 13.4866 41.4981 18.2361C32.7932 22.9856 25.4257 29.8523 20.076 38.2017C14.7262 46.5512 11.5669 56.1142 10.8899 66.0073C10.2128 75.9005 12.0398 85.8047 16.2021 94.8052C18.1994 99.2389 18.4401 104.265 16.8756 108.87L10.8357 126.868C10.7261 127.184 10.7111 127.525 10.7925 127.849C10.874 128.174 11.0483 128.468 11.2942 128.695C11.5244 128.94 11.8206 129.114 12.1473 129.195C12.4739 129.277 12.8171 129.262 13.1356 129.153L31.0475 123.142C35.6933 121.544 40.7744 121.785 45.2481 123.815C53.2746 127.497 62.0243 129.333 70.8539 129.188C79.6834 129.043 88.368 126.92 96.2691 122.976C104.17 119.032 111.087 113.367 116.51 106.398C121.933 99.4287 125.725 91.3324 127.606 82.7045C129.488 74.0766 129.412 65.1366 127.383 56.5421C125.354 47.9476 121.425 39.9172 115.883 33.0415C110.342 26.1659 103.33 20.6199 95.3624 16.8114C87.3952 13.0029 78.6755 11.0289 69.8448 11.0345L69.8807 11.0274ZM103.878 55.8144C103.878 54.3892 103.311 53.0225 102.304 52.0147C101.296 51.007 99.9291 50.4408 98.504 50.4408H41.1857C39.7605 50.4408 38.3937 51.007 37.386 52.0147C36.3783 53.0225 35.8121 54.3892 35.8121 55.8144C35.8121 57.2396 36.3783 58.6064 37.386 59.6141C38.3937 60.6219 39.7605 61.188 41.1857 61.188H98.504C99.9291 61.188 101.296 60.6219 102.304 59.6141C103.311 58.6064 103.878 57.2396 103.878 55.8144ZM82.3832 84.4735C82.3832 83.0484 81.8171 81.6816 80.8093 80.6738C79.8016 79.6661 78.4348 79.1 77.0096 79.1H41.1857C39.7605 79.1 38.3937 79.6661 37.386 80.6738C36.3783 81.6816 35.8121 83.0484 35.8121 84.4735C35.8121 85.8987 36.3783 87.2655 37.386 88.2732C38.3937 89.281 39.7605 89.8471 41.1857 89.8471H77.0096C78.4348 89.8471 79.8016 89.281 80.8093 88.2732C81.8171 87.2655 82.3832 85.8987 82.3832 84.4735Z" />
        </svg>
    );
};

export default SupportIcon;
