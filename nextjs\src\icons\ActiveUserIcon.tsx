import React from 'react';

const ActiveUserIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M64.2226 49.3151H25.7877C11.5685 49.3151 0 60.8836 0 75.1027C0 83.3116 6.68836 90 14.9075 90H28.0479C28.5411 90 29.0137 89.8048 29.363 89.4555C29.7123 89.1062 29.8973 88.6438 29.8973 88.1507C29.8973 87.6575 29.7021 87.1849 29.363 86.8459C29.0137 86.4966 28.5411 86.3014 28.0479 86.3014H14.9075C11.9178 86.3014 9.10274 85.1404 6.9863 83.024C4.86986 80.9075 3.69863 78.0925 3.69863 75.1027C3.69863 62.9178 13.613 53.0137 25.7877 53.0137H45.7295C57.9041 53.0137 67.8082 62.9178 67.8082 75.1027C67.8082 78.0925 66.6473 80.9075 64.5308 83.024C62.4144 85.1404 59.5993 86.3014 56.6096 86.3014H43.4589C42.976 86.3014 42.5034 86.4966 42.1541 86.8459C41.8151 87.1849 41.6096 87.6678 41.6096 88.1507C41.6096 88.6336 41.8048 89.1062 42.1541 89.4555C42.5034 89.8048 42.976 90 43.4589 90H75.1027C83.3219 90 90 83.3116 90 75.1027C90 60.8836 78.4418 49.3151 64.2226 49.3151ZM83.024 83.024C80.9075 85.1404 78.0925 86.3014 75.1027 86.3014H66.75L68.3219 84.3082C70.387 81.6986 71.5171 78.4315 71.5069 75.1027C71.5069 71.3219 70.6644 67.5103 69.0514 64.0993C67.4384 60.6781 65.0445 57.5959 62.1267 55.2021L59.4658 53.0137H64.2226C76.3973 53.0137 86.3014 62.9178 86.3014 75.1027C86.3014 78.0925 85.1404 80.9075 83.024 83.024ZM54.2466 0C51.1952 0 48.2671 0.657534 45.5342 1.96233L45 2.2089L44.476 1.96233C41.774 0.678082 38.774 0 35.7534 0C24.5445 0 15.411 9.12329 15.411 20.3425C15.411 31.5616 24.5445 40.6849 35.7534 40.6849C38.8048 40.6849 41.7432 40.0274 44.476 38.7329L45 38.476L45.5342 38.7329C48.226 40.0069 51.2363 40.6849 54.2158 40.6849H54.2466C65.4658 40.6849 74.589 31.5616 74.589 20.3425C74.589 9.12329 65.4658 0 54.2466 0ZM35.7534 36.9863C26.5788 36.9863 19.1096 29.5171 19.1096 20.3425C19.1096 11.1678 26.5788 3.69863 35.7534 3.69863C44.9281 3.69863 52.3973 11.1678 52.3973 20.3425C52.3973 29.5171 44.9384 36.9863 35.7534 36.9863ZM54.2466 36.9863C53.137 36.9863 52.0069 36.8733 50.9178 36.6575L48.6986 36.2055L50.2808 34.5822C54.0308 30.7603 56.0959 25.7055 56.0959 20.3425C56.0959 14.9795 54.0411 9.92466 50.2808 6.10274L48.6884 4.47945L50.9178 4.03767C52.0069 3.81164 53.1267 3.69863 54.2466 3.69863C63.4315 3.69863 70.8904 11.1678 70.8904 20.3425C70.8904 29.5171 63.4315 36.9863 54.2466 36.9863Z" />
        <path fillRule="evenodd" clipRule="evenodd" d="M54.2466 0C51.1952 0 48.2671 0.657534 45.5342 1.96233L45 2.2089L44.476 1.96233C41.774 0.678082 38.774 0 35.7534 0C24.5445 0 15.411 9.12329 15.411 20.3425C15.411 31.5616 24.5445 40.6849 35.7534 40.6849C38.8048 40.6849 41.7432 40.0274 44.476 38.7329L45 38.476L45.5342 38.7329C48.226 40.0069 51.2363 40.6849 54.2158 40.6849H54.2466C65.4658 40.6849 74.589 31.5616 74.589 20.3425C74.589 9.12329 65.4658 0 54.2466 0ZM35.7534 36.9863C26.5788 36.9863 19.1096 29.5171 19.1096 20.3425C19.1096 11.1678 26.5788 3.69863 35.7534 3.69863C44.9281 3.69863 52.3973 11.1678 52.3973 20.3425C52.3973 29.5171 44.9384 36.9863 35.7534 36.9863ZM54.2466 36.9863C53.137 36.9863 52.0069 36.8733 50.9178 36.6575L48.6986 36.2055L50.2808 34.5822C54.0308 30.7603 56.0959 25.7055 56.0959 20.3425C56.0959 14.9795 54.0411 9.92466 50.2808 6.10274L48.6884 4.47945L50.9178 4.03767C52.0069 3.81164 53.1267 3.69863 54.2466 3.69863C63.4315 3.69863 70.8904 11.1678 70.8904 20.3425C70.8904 29.5171 63.4315 36.9863 54.2466 36.9863ZM64.2226 49.3151H25.7877C11.5685 49.3151 0 60.8836 0 75.1027C0 83.3116 6.68836 90 14.9075 90H28.0479C28.5411 90 29.0137 89.8048 29.363 89.4555C29.7123 89.1062 29.8973 88.6438 29.8973 88.1507C29.8973 87.6575 29.7021 87.1849 29.363 86.8459C29.0137 86.4966 28.5411 86.3014 28.0479 86.3014H14.9075C11.9178 86.3014 9.10274 85.1404 6.9863 83.024C4.86986 80.9075 3.69863 78.0925 3.69863 75.1027C3.69863 62.9178 13.613 53.0137 25.7877 53.0137H45.7295C57.9041 53.0137 67.8082 62.9178 67.8082 75.1027C67.8082 78.0925 66.6473 80.9075 64.5308 83.024C62.4144 85.1404 59.5993 86.3014 56.6096 86.3014H43.4589C42.976 86.3014 42.5034 86.4966 42.1541 86.8459C41.8151 87.1849 41.6096 87.6678 41.6096 88.1507C41.6096 88.6336 41.8048 89.1062 42.1541 89.4555C42.5034 89.8048 42.976 90 43.4589 90H75.1027C83.3219 90 90 83.3116 90 75.1027C90 60.8836 78.4418 49.3151 64.2226 49.3151ZM83.024 83.024C80.9075 85.1404 78.0925 86.3014 75.1027 86.3014H66.75L68.3219 84.3082C70.387 81.6986 71.5171 78.4315 71.5069 75.1027C71.5069 71.3219 70.6644 67.5103 69.0514 64.0993C67.4384 60.6781 65.0445 57.5959 62.1267 55.2021L59.4658 53.0137H64.2226C76.3973 53.0137 86.3014 62.9178 86.3014 75.1027C86.3014 78.0925 85.1404 80.9075 83.024 83.024Z" />
    </svg>
  );
}

export default ActiveUserIcon;
