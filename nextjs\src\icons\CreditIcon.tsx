import React from 'react';

const CreditIcon = ({ width, height, className }: any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 14 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M12.4492 3.33496C12.4492 3.29958 12.4283 3.17808 12.1875 2.98828C11.9528 2.80329 11.5782 2.61267 11.0674 2.44238C10.0514 2.10379 8.61302 1.88477 6.99902 1.88477C5.38503 1.88477 3.94662 2.10379 2.93066 2.44238C2.4198 2.61267 2.04528 2.80329 1.81055 2.98828C1.56978 3.17808 1.54883 3.29958 1.54883 3.33496C1.54883 3.37034 1.56978 3.49185 1.81055 3.68164C2.04528 3.86663 2.4198 4.05725 2.93066 4.22754C3.94662 4.56613 5.38503 4.78516 6.99902 4.78516C8.61302 4.78516 10.0514 4.56613 11.0674 4.22754C11.5782 4.05725 11.9528 3.86663 12.1875 3.68164C12.4283 3.49185 12.4492 3.37034 12.4492 3.33496ZM13.5488 3.33496C13.5488 3.85186 13.2345 4.25722 12.8682 4.5459C12.4958 4.83934 11.9907 5.07891 11.416 5.27051C10.2604 5.65571 8.69854 5.88477 6.99902 5.88477C5.2995 5.88477 3.73762 5.65571 2.58203 5.27051C2.00737 5.07891 1.50226 4.83934 1.12988 4.5459C0.763581 4.25722 0.449219 3.85186 0.449219 3.33496C0.449219 2.81807 0.763581 2.4127 1.12988 2.12402C1.50226 1.83058 2.00737 1.59101 2.58203 1.39941C3.73762 1.01422 5.2995 0.785156 6.99902 0.785156C8.69854 0.785156 10.2604 1.01422 11.416 1.39941C11.9907 1.59101 12.4958 1.83058 12.8682 2.12402C13.2345 2.4127 13.5488 2.81807 13.5488 3.33496Z" />
            <path d="M0.449219 12.668V3.33496C0.449219 3.0312 0.695267 2.78516 0.999023 2.78516C1.30278 2.78516 1.54883 3.0312 1.54883 3.33496V12.668L1.55566 12.707C1.57156 12.7619 1.62526 12.8636 1.80078 13.0049C2.02964 13.1891 2.40202 13.3843 2.93066 13.5605C3.98432 13.9117 5.4481 14.1182 6.99902 14.1182C8.54994 14.1182 10.0137 13.9117 11.0674 13.5605C11.596 13.3843 11.9684 13.1891 12.1973 13.0049C12.3728 12.8636 12.4265 12.7619 12.4424 12.707L12.4492 12.668V3.33496C12.4492 3.0312 12.6953 2.78516 12.999 2.78516C13.3028 2.78516 13.5488 3.0312 13.5488 3.33496V12.668L13.5352 12.8516C13.473 13.2673 13.2046 13.6065 12.8867 13.8623C12.5183 14.1587 12.0123 14.4057 11.416 14.6045C10.2193 15.0034 8.63057 15.2188 6.99902 15.2188C5.36748 15.2187 3.7788 15.0034 2.58203 14.6045C1.98577 14.4057 1.47975 14.1587 1.11133 13.8623C0.793418 13.6065 0.525092 13.2673 0.462891 12.8516L0.449219 12.668Z" />
            <path d="M0.449219 8.00098C0.449219 7.69722 0.695267 7.45117 0.999023 7.45117C1.30278 7.45117 1.54883 7.69722 1.54883 8.00098C1.54883 8.03111 1.56684 8.14963 1.80078 8.33789C2.02966 8.52203 2.40213 8.71738 2.93066 8.89355C3.98431 9.2447 5.44815 9.45117 6.99902 9.45117C8.5499 9.45117 10.0137 9.2447 11.0674 8.89355C11.5959 8.71738 11.9684 8.52203 12.1973 8.33789C12.4312 8.14963 12.4492 8.03111 12.4492 8.00098C12.4492 7.69722 12.6953 7.45117 12.999 7.45117C13.3028 7.45117 13.5488 7.69722 13.5488 8.00098C13.5488 8.50127 13.2502 8.90284 12.8867 9.19531C12.5184 9.49161 12.0121 9.73778 11.416 9.93652C10.2193 10.3354 8.63057 10.5508 6.99902 10.5508C5.36748 10.5508 3.7788 10.3354 2.58203 9.93652C1.98594 9.73778 1.47969 9.49161 1.11133 9.19531C0.747896 8.90284 0.449219 8.50127 0.449219 8.00098Z" />
        </svg>
    );
};

export default CreditIcon;
