const EditIcon = ({height, width, className}:any) => {
    return (
        <svg className={className} width={width} height={height}
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M15.5506 1.53L14.5706 0.55C14.2106 0.19 13.7406 0 13.2406 0C12.7406 0 12.2606 0.2 11.9106 0.55L4.02055 8.44C3.89055 8.57 3.81055 8.76 3.81055 8.94V11.58C3.81055 11.97 4.13055 12.29 4.52055 12.29H7.16055C7.35055 12.29 7.53055 12.21 7.66055 12.08L15.5506 4.19C15.9106 3.83 16.1006 3.36 16.1006 2.86C16.1006 2.36 15.9006 1.88 15.5506 1.53ZM12.0906 2.37L12.9106 1.56C12.9906 1.48 13.1106 1.43 13.2406 1.43C13.3706 1.43 13.4806 1.48 13.5706 1.56L14.5506 2.54C14.6406 2.63 14.6806 2.74 14.6806 2.87C14.6806 3 14.6306 3.11 14.5406 3.2L13.7906 4.02L12.0906 2.39V2.37ZM5.21055 9.25L11.1206 3.34L12.5706 4.79L12.7906 4.94L6.88055 10.87L5.23055 10.92V9.25H5.21055Z" />
            <path d="M14.28 7.89011C13.89 7.89011 13.57 8.21011 13.57 8.60011V12.9201C13.57 13.8901 12.78 14.6701 11.82 14.6801H3.18C2.21 14.6801 1.43 13.8901 1.42 12.9201V4.29011C1.42 3.32011 2.21 2.54011 3.18 2.53011H7.5C7.89 2.53011 8.21 2.21011 8.21 1.82011C8.21 1.43011 7.89 1.11011 7.5 1.11011H3.18C1.43 1.11011 0 2.54011 0 4.29011V12.9201C0 14.6701 1.43 16.1001 3.18 16.1001H11.81C13.56 16.1001 14.99 14.6701 14.99 12.9201V8.60011C14.99 8.21011 14.67 7.89011 14.28 7.89011Z" />
        </svg>
    );
};

export default EditIcon;
