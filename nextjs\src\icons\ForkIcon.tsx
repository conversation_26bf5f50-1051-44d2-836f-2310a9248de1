import React from 'react';

const ForkIcon = ({className, width, height}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14.8512 16.2C15.0285 16.2 15.204 16.1651 15.3678 16.0972C15.5316 16.0294 15.6804 15.93 15.8058 15.8046C15.9312 15.6792 16.0306 15.5304 16.0984 15.3666C16.1663 15.2028 16.2012 15.0273 16.2012 14.85C16.2012 14.6727 16.1663 14.4972 16.0984 14.3334C16.0306 14.1696 15.9312 14.0208 15.8058 13.8954C15.6804 13.77 15.5316 13.6706 15.3678 13.6028C15.204 13.5349 15.0285 13.5 14.8512 13.5C14.4932 13.5 14.1498 13.6422 13.8966 13.8954C13.6434 14.1486 13.5012 14.492 13.5012 14.85C13.5012 15.208 13.6434 15.5514 13.8966 15.8046C14.1498 16.0578 14.4932 16.2 14.8512 16.2ZM18.0012 14.85C18.0011 15.4342 17.8385 16.0068 17.5316 16.5038C17.2248 17.0009 16.7857 17.4029 16.2636 17.6648C15.7414 17.9267 15.1567 18.0383 14.5748 17.987C13.9929 17.9358 13.4367 17.7237 12.9684 17.3746C12.5001 17.0254 12.1381 16.5529 11.9228 16.0098C11.7076 15.4668 11.6476 14.8745 11.7496 14.2994C11.8515 13.7242 12.1114 13.1887 12.5002 12.7527C12.889 12.3167 13.3914 11.9974 13.9512 11.8305C13.9512 11.34 13.9494 11.0178 13.9296 10.7712C13.9098 10.5264 13.8756 10.4364 13.8531 10.3914C13.7668 10.2221 13.6291 10.0844 13.4598 9.9981C13.4148 9.9756 13.3248 9.9414 13.08 9.9216C12.8262 9.9 12.4905 9.9 11.9712 9.9H6.93116C6.41186 9.9 6.07706 9.9 5.82236 9.9216C5.57756 9.9414 5.48756 9.9756 5.44256 9.9981C5.27322 10.0844 5.13555 10.2221 5.04926 10.3914C5.02676 10.4364 4.99256 10.5264 4.97276 10.7712C4.95116 11.0178 4.95116 11.34 4.95116 11.8305C5.67446 12.0485 6.29541 12.519 6.7009 13.1564C7.1064 13.7938 7.26947 14.5557 7.16042 15.3032C7.05137 16.0507 6.67746 16.7342 6.10677 17.2292C5.53609 17.7242 4.8066 17.9977 4.05116 18C3.29405 18.0007 2.56202 17.7286 1.98911 17.2336C1.4162 16.7387 1.04072 16.0539 0.931437 15.3047C0.822146 14.5555 0.98635 13.792 1.39398 13.154C1.80162 12.516 2.42541 12.0462 3.15116 11.8305C3.15116 11.3625 3.15116 10.9575 3.17816 10.6245C3.20786 10.269 3.27176 9.9144 3.44546 9.5742C3.70432 9.06618 4.11735 8.65315 4.62536 8.3943C4.96556 8.2206 5.32016 8.1567 5.67566 8.1279C6.01226 8.1 6.42266 8.1 6.89606 8.1H8.55116V6.1695C7.82786 5.9515 7.20692 5.48096 6.80142 4.84357C6.39592 4.20618 6.23285 3.44435 6.3419 2.69682C6.45095 1.94929 6.82486 1.26579 7.39555 0.770812C7.96623 0.275828 8.69572 0.00228428 9.45116 1.17715e-06C10.2083 -0.000653044 10.9403 0.271409 11.5133 0.766377C12.0862 1.26135 12.4616 1.94611 12.5709 2.69529C12.6802 3.44448 12.516 4.20797 12.1084 4.84598C11.7007 5.48399 11.077 5.95383 10.3512 6.1695V8.1H12.0063C12.4806 8.1 12.8892 8.1 13.2267 8.127C13.5822 8.1567 13.9368 8.2206 14.277 8.3943C14.785 8.65315 15.198 9.06618 15.4569 9.5742C15.6306 9.9144 15.6945 10.269 15.7233 10.6245C15.7512 10.9575 15.7512 11.3625 15.7512 11.8305C16.4013 12.0246 16.9713 12.4233 17.3767 12.9673C17.7821 13.5113 18.0011 14.1716 18.0012 14.85ZM4.05116 16.2C4.22845 16.2 4.40399 16.1651 4.56778 16.0972C4.73157 16.0294 4.8804 15.93 5.00575 15.8046C5.13111 15.6792 5.23055 15.5304 5.2984 15.3666C5.36624 15.2028 5.40116 15.0273 5.40116 14.85C5.40116 14.6727 5.36624 14.4972 5.2984 14.3334C5.23055 14.1696 5.13111 14.0208 5.00575 13.8954C4.8804 13.77 4.73157 13.6706 4.56778 13.6028C4.40399 13.5349 4.22845 13.5 4.05116 13.5C3.69312 13.5 3.34974 13.6422 3.09657 13.8954C2.84339 14.1486 2.70116 14.492 2.70116 14.85C2.70116 15.208 2.84339 15.5514 3.09657 15.8046C3.34974 16.0578 3.69312 16.2 4.05116 16.2ZM9.45116 4.5C9.8092 4.5 10.1526 4.35777 10.4058 4.10459C10.659 3.85142 10.8012 3.50804 10.8012 3.15C10.8012 2.79196 10.659 2.44858 10.4058 2.19541C10.1526 1.94223 9.8092 1.8 9.45116 1.8C9.09312 1.8 8.74974 1.94223 8.49657 2.19541C8.24339 2.44858 8.10116 2.79196 8.10116 3.15C8.10116 3.50804 8.24339 3.85142 8.49657 4.10459C8.74974 4.35777 9.09312 4.5 9.45116 4.5Z"/>
    </svg>    
  );
}

export default ForkIcon;
