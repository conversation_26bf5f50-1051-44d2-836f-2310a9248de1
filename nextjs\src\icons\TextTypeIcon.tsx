const TextTypeIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 20 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.7462 3.15771C16.8554 3.15771 17.6789 3.40673 18.2165 3.90475C18.7541 4.40277 19.023 5.19789 19.023 6.29015V12.6145H17.9958L17.7241 11.2392H17.6562C17.3959 11.5788 17.1242 11.8646 16.8413 12.0966C16.5583 12.323 16.2301 12.4956 15.8565 12.6145C15.4888 12.7276 15.0359 12.7842 14.4983 12.7842C13.9324 12.7842 13.4288 12.6852 12.9873 12.4871C12.5515 12.289 12.2063 11.9891 11.9516 11.5873C11.7027 11.1854 11.5781 10.6761 11.5781 10.0592C11.5781 9.13111 11.946 8.41804 12.6817 7.92002C13.4174 7.422 14.5379 7.15036 16.0433 7.10508L17.6477 7.03717V6.46841C17.6477 5.66479 17.4752 5.10168 17.1299 4.7791C16.7847 4.45653 16.298 4.29524 15.6698 4.29524C15.1831 4.29524 14.7191 4.36598 14.2776 4.50746C13.8362 4.64895 13.4174 4.81589 13.0213 5.0083L12.5883 3.9387C13.0071 3.72364 13.4881 3.53971 14.0314 3.38691C14.5748 3.23411 15.1463 3.15771 15.7462 3.15771ZM17.6308 8.03038L16.2131 8.0898C15.053 8.13507 14.2352 8.32466 13.7598 8.65857C13.2844 8.99246 13.0468 9.46502 13.0468 10.0762C13.0468 10.6082 13.208 11.0015 13.5305 11.2561C13.8532 11.5109 14.2804 11.6382 14.8124 11.6382C15.6387 11.6382 16.315 11.409 16.8413 10.9506C17.3676 10.4922 17.6308 9.80458 17.6308 8.88777V8.03038Z"/>
    <path d="M9.48218 12.4618L7.95416 8.52292H2.97963L1.46011 12.4618H0L4.85568 0H6.15449L10.9762 12.4618H9.48218ZM7.52122 7.24108L6.06961 3.32767C6.03565 3.22581 5.97906 3.05885 5.89983 2.82682C5.82626 2.59479 5.74986 2.35427 5.67063 2.10526C5.5914 1.85625 5.52632 1.65535 5.47538 1.50255C5.41879 1.73458 5.35654 1.96661 5.28862 2.19864C5.22637 2.42501 5.16412 2.63724 5.10186 2.83532C5.03961 3.02773 4.98585 3.19185 4.94058 3.32767L3.46349 7.24108H7.52122Z" />
    <path d="M0 14.2104H20"/>
    </svg>
  );
}

export default TextTypeIcon;
