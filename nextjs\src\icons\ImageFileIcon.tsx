import React from 'react';

const ImageFileIcon = ({ width, height, className }:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.49153 0H14.5085C14.9038 0.000716877 15.2828 0.158089 15.5624 0.437649C15.8419 0.717209 15.9993 1.09617 16 1.49153V8.19525C16 8.23181 15.995 8.26799 15.9854 8.30286C15.9951 8.33777 16.0001 8.37411 16.0001 8.41096V14.7242C15.9994 15.1195 15.842 15.4985 15.5625 15.7781C15.2829 16.0576 14.904 16.215 14.5086 16.2157H1.49166C1.09631 16.215 0.717346 16.0576 0.437786 15.7781C0.158226 15.4985 0.000853824 15.1195 0.000136946 14.7242V11.7954C-0.000847812 11.7573 0.00352521 11.7194 0.0130327 11.6827C0.00434528 11.6492 -7.73553e-05 11.6146 1.0238e-06 11.5797V1.49153C0.000718937 1.09617 0.158092 0.717209 0.437651 0.437649C0.717211 0.158089 1.09617 0.000716877 1.49153 0Z"
                fill="#D93025"
            />
            <path
                d="M13.4834 8.25861L11.3146 6.08588C11.2634 6.03447 11.1948 6.00399 11.1223 6.00036C11.0498 5.99674 10.9786 6.02023 10.9224 6.06627L6.66129 9.56069L5.41608 8.31352C5.36483 8.26212 5.29627 8.23163 5.22377 8.22801C5.15127 8.22438 5.08002 8.24788 5.02389 8.29391L2.10795 10.6784C2.07322 10.7068 2.04544 10.7428 2.02675 10.7836C2.00805 10.8244 1.99894 10.8689 2.0001 10.9137V13.0316C2.00062 13.3175 2.11441 13.5915 2.31656 13.7936C2.51871 13.9958 2.79274 14.1096 3.07862 14.1101H12.4912C12.7771 14.1096 13.0511 13.9958 13.2533 13.7936C13.4554 13.5915 13.5692 13.3175 13.5697 13.0316V8.46647C13.5697 8.38849 13.5386 8.31372 13.4834 8.25861Z"
                fill="white"
            />
            <path
                d="M3.48105 2C3.188 2 2.90153 2.08694 2.65791 2.24982C2.41428 2.41269 2.22445 2.64418 2.11245 2.91499C2.00044 3.18579 1.97129 3.48374 2.02868 3.77112C2.08608 4.0585 2.22744 4.32239 2.43487 4.5294C2.64231 4.73641 2.90649 4.87722 3.19399 4.93402C3.48148 4.99083 3.77937 4.96106 4.04994 4.8485C4.32052 4.73593 4.55161 4.54563 4.71399 4.30167C4.87636 4.05771 4.96271 3.77106 4.96211 3.47801C4.9613 3.08574 4.80491 2.70981 4.52724 2.43271C4.24958 2.15562 3.87333 2 3.48105 2Z"
                fill="white"
            />
        </svg>
    );
};

export default ImageFileIcon;
