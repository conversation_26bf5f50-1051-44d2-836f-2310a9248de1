const Customgpt = ({ height, width, className }: any) => {
    return (
        // <svg className={className} width={width} height={height}
        //     viewBox="0 0 16 16"
        //     fill="none"
        //     xmlns="http://www.w3.org/2000/svg"
        // >
        //     <path
        //         fillRule="evenodd"
        //         clipRule="evenodd"
        //         d="M2.94281 0.39049C3.19284 0.640517 3.3333 0.979626 3.3333 1.33322C3.33325 1.59858 3.2539 1.85787 3.10544 2.07782C2.95698 2.29777 2.74618 2.46834 2.50009 2.56763V5.03952C3.93381 3.56869 5.86977 2.66675 8.0001 2.66675C10.1309 2.66675 12.0667 3.56852 13.5002 5.03909V2.56763C13.2542 2.4683 13.0434 2.29773 12.8949 2.07779C12.7464 1.85786 12.667 1.59858 12.6668 1.33322C12.6729 0.983572 12.816 0.650295 13.0654 0.405172C13.3148 0.16005 13.6505 0.0226956 14.0002 0.0226956C14.3499 0.0226956 14.6856 0.16005 14.935 0.405172C15.1845 0.650295 15.3276 0.983572 15.3337 1.33322C15.3335 1.59864 15.2541 1.85798 15.1056 2.07798C14.9572 2.29799 14.7464 2.46863 14.5002 2.56803V6.27922C15.4443 7.70185 16.0002 9.44724 16.0002 11.3337C16.0002 13.9119 12.4196 16.0001 8.0001 16.0001C3.58204 16.0001 0 13.9119 0 11.3337C0 9.44742 0.556003 7.70218 1.50008 6.27963V2.56763C1.25404 2.46827 1.04329 2.29768 0.894838 2.07775C0.746387 1.85782 0.667006 1.59856 0.66687 1.33322C0.66687 0.979626 0.807334 0.640517 1.05736 0.39049C1.30739 0.140463 1.6465 0 2.00009 0C2.35368 0 2.69279 0.140463 2.94281 0.39049ZM4.00011 7.33329C6.66935 8.2553 9.53138 8.1889 12.0002 7.33329C12.6188 8.3569 13.0002 9.77092 13.0002 11.3335C13.0002 12.07 10.7614 12.6668 8.00016 12.6668C5.23773 12.6668 3.0001 12.07 3.0001 11.3335C3.0001 9.77092 3.3817 8.3569 4.00011 7.33329ZM5.18112 11.2702C5.28226 11.3121 5.39066 11.3336 5.50013 11.3335C5.6096 11.3336 5.718 11.3121 5.81914 11.2702C5.92028 11.2283 6.01217 11.1669 6.08956 11.0895C6.16694 11.0121 6.22831 10.9201 6.27014 10.819C6.31197 10.7178 6.33345 10.6094 6.33334 10.4999C6.33334 10.0397 5.96074 9.66672 5.50013 9.66672C5.03953 9.66672 4.66692 10.0397 4.66692 10.4999C4.66682 10.6094 4.68829 10.7178 4.73012 10.819C4.77195 10.9201 4.83332 11.0121 4.9107 11.0895C4.98809 11.1669 5.07998 11.2283 5.18112 11.2702ZM10.1811 11.2703C10.2823 11.3122 10.3907 11.3336 10.5002 11.3335C10.6097 11.3336 10.7181 11.3122 10.8193 11.2703C10.9205 11.2284 11.0124 11.167 11.0898 11.0896C11.1672 11.0122 11.2286 10.9202 11.2704 10.8191C11.3123 10.7179 11.3337 10.6094 11.3336 10.4999C11.3336 10.0397 10.9612 9.66672 10.5002 9.66672C10.0392 9.66672 9.66678 10.0397 9.66678 10.4999C9.66665 10.6094 9.68812 10.7179 9.72995 10.8191C9.77178 10.9202 9.83317 11.0122 9.91058 11.0896C9.98799 11.167 10.0799 11.2284 10.1811 11.2703ZM1.33322 11.3337C1.33322 12.9103 4.07125 14.6669 8.0001 14.6669C11.9293 14.6669 14.6668 12.9103 14.6668 11.3337C14.6668 7.28981 11.6759 3.99996 8.0001 3.99996C4.32385 3.99996 1.33322 7.28981 1.33322 11.3337Z"
        //     />
        // </svg>
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 208 208"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M55.6279 140.279H16.9302C12.4416 140.274 8.13833 138.489 4.96441 135.315C1.79048 132.141 0.00512106 127.837 0 123.349V84.6512C0.00512106 80.1626 1.79048 75.8593 4.96441 72.6853C8.13833 69.5114 12.4416 67.7261 16.9302 67.7209H55.6279C60.1165 67.7261 64.4198 69.5114 67.5937 72.6853C70.7677 75.8593 72.553 80.1626 72.5581 84.6512V123.349C72.553 127.837 70.7677 132.141 67.5937 135.315C64.4198 138.489 60.1165 140.274 55.6279 140.279ZM16.9302 82.2326C16.2888 82.2326 15.6736 82.4874 15.22 82.9409C14.7664 83.3945 14.5116 84.0097 14.5116 84.6512V123.349C14.5116 123.99 14.7664 124.605 15.22 125.059C15.6736 125.513 16.2888 125.767 16.9302 125.767H55.6279C56.2694 125.767 56.8845 125.513 57.3381 125.059C57.7917 124.605 58.0465 123.99 58.0465 123.349V84.6512C58.0465 84.0097 57.7917 83.3945 57.3381 82.9409C56.8845 82.4874 56.2694 82.2326 55.6279 82.2326H16.9302ZM191.07 208H152.372C147.883 207.995 143.58 206.21 140.406 203.036C137.232 199.862 135.447 195.558 135.442 191.07V152.372C135.447 147.883 137.232 143.58 140.406 140.406C143.58 137.232 147.883 135.447 152.372 135.442H191.07C195.558 135.447 199.862 137.232 203.036 140.406C206.21 143.58 207.995 147.883 208 152.372V191.07C207.995 195.558 206.21 199.862 203.036 203.036C199.862 206.21 195.558 207.995 191.07 208ZM152.372 149.953C151.731 149.953 151.115 150.208 150.662 150.662C150.208 151.115 149.953 151.731 149.953 152.372V191.07C149.953 191.711 150.208 192.326 150.662 192.78C151.115 193.234 151.731 193.488 152.372 193.488H191.07C191.711 193.488 192.326 193.234 192.78 192.78C193.234 192.326 193.488 191.711 193.488 191.07V152.372C193.488 151.731 193.234 151.115 192.78 150.662C192.326 150.208 191.711 149.953 191.07 149.953H152.372ZM191.07 72.5581H152.372C147.883 72.553 143.58 70.7677 140.406 67.5937C137.232 64.4198 135.447 60.1165 135.442 55.6279V16.9302C135.447 12.4416 137.232 8.13833 140.406 4.96441C143.58 1.79048 147.883 0.00512106 152.372 0H191.07C195.558 0.00512106 199.862 1.79048 203.036 4.96441C206.21 8.13833 207.995 12.4416 208 16.9302V55.6279C207.995 60.1165 206.21 64.4198 203.036 67.5937C199.862 70.7677 195.558 72.553 191.07 72.5581ZM152.372 14.5116C151.731 14.5116 151.115 14.7664 150.662 15.22C150.208 15.6736 149.953 16.2888 149.953 16.9302V55.6279C149.953 56.2694 150.208 56.8845 150.662 57.3381C151.115 57.7917 151.731 58.0465 152.372 58.0465H191.07C191.711 58.0465 192.326 57.7917 192.78 57.3381C193.234 56.8845 193.488 56.2694 193.488 55.6279V16.9302C193.488 16.2888 193.234 15.6736 192.78 15.22C192.326 14.7664 191.711 14.5116 191.07 14.5116H152.372Z" />
            <path d="M142.698 178.977H113.674C109.186 178.972 104.883 177.186 101.709 174.013C98.5347 170.839 96.7493 166.535 96.7442 162.047V45.9537C96.7493 41.4651 98.5347 37.1618 101.709 33.9878C104.883 30.8139 109.186 29.0286 113.674 29.0234H142.698C144.622 29.0234 146.468 29.7879 147.828 31.1486C149.189 32.5093 149.953 34.3549 149.953 36.2793C149.953 38.2036 149.189 40.0492 147.828 41.4099C146.468 42.7706 144.622 43.5351 142.698 43.5351H113.674C113.033 43.5351 112.418 43.7899 111.964 44.2435C111.511 44.697 111.256 45.3122 111.256 45.9537V162.047C111.256 162.688 111.511 163.303 111.964 163.757C112.418 164.21 113.033 164.465 113.674 164.465H142.698C144.622 164.465 146.468 165.23 147.828 166.59C149.189 167.951 149.953 169.797 149.953 171.721C149.953 173.645 149.189 175.491 147.828 176.852C146.468 178.212 144.622 178.977 142.698 178.977Z" />
            <path
                d="M104 111.256H65.3023C63.378 111.256 61.5324 110.491 60.1717 109.131C58.811 107.77 58.0465 105.924 58.0465 104C58.0465 102.076 58.811 100.23 60.1717 98.8693C61.5324 97.5086 63.378 96.7441 65.3023 96.7441H104C105.924 96.7441 107.77 97.5086 109.131 98.8693C110.491 100.23 111.256 102.076 111.256 104C111.256 105.924 110.491 107.77 109.131 109.131C107.77 110.491 105.924 111.256 104 111.256Z"
            />
        </svg>
    );
};

export default Customgpt;
