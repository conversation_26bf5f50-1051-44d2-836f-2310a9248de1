from src.db.config import db_instance
from pymongo.errors import PyMongoError

class CustomGptDocConfig:
    MAX_TOKEN_LIMIT = 10000
    TOP_K=18

class CustomGptChatConfig:
    MAX_TOKEN_LIMIT = 10000

class DEFAULTMODEL:
      GPT_4o_MINI ='gpt-4.1-mini'
    
class GetLLMkey:
    def __init__(self):
        """
        Initialize the repository.

        Args:
            db_instance: An instance of the database.
        """
        self.db_instance = db_instance
        
    def default_llm_key(self,company_id:str = None,query:dict=None,projection:dict=None,companymodel:str = None):
        self.instance = self.db_instance.get_collection(companymodel)
        try:
            result = self.instance.find_one(query,projection)
            if not result:
                raise ValueError(f"No data found for company id: {company_id}")
            return str(result['_id'])
        except PyMongoError as e:
            raise



class ImageGenerateConfig:
    MAX_TOKEN_LIMIT = 10000
    LLM_IMAGE_MODEL = 'gpt-image-1'
    DALLE_WRAPPER_SIZE = "1024x1024"
    DALLE_WRAPPER_QUALITY = 'high'
    DALLE_WRAPPER_STYLE = 'vivid'
    n = 1


class ToolChatConfig:
    MAX_TOKEN_LIMIT = 10000
    VISION_MODELS = {'gpt-4.1': True, 'gpt-4.1-mini': True, 'gpt-4.1-nano': True, 'gpt-4o-2024-11-20':True}
    TEMPRATURE = 0.7
    IMAGE_SIZE_LIST = ["1024x1024","1792x1024","1024x1792","1024x1536","1536x1024"]

