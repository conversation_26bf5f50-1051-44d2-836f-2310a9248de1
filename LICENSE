Open Source License
Weam is licensed under a modified version of the Apache License 2.0 with the following extra conditions.

What You Can Do
 Use Weam for commercial or non‑commercial deployments inside a single organization.
 Use Weam to build and deliver solutions for your clients, as long as each client runs on its own dedicated infrastructure (not shared across multiple clients).
 Contribute improvements or fixes under an open development model.

We encourage innovation and collaboration — but please read the full license before deploying or redistributing.

What You Cannot Do (without a commercial agreement)
 Offer Weam (or a modified version) as a hosted SaaS or managed service for multiple customers.
 Run multi‑tenant or separate single‑tenant instances for unrelated third parties.

If you want to provide Weam as a service to others, you must get a commercial license from Weam AI Pvt Ltd.

Additional Conditions : 

1. Contributions : By contributing code, you allow the maintainers to include it in future versions under different licensing (more restrictive or more permissive).
Your contributions may be used in commercial products run by the maintainers (for example, Weam Cloud).
Contributors are still bound by the “no SaaS” rule above.

2. Third‑Party Dependencies : If you modify or redistribute third‑party packages bundled with Weam, you are responsible for following those packages’ licenses.

3. Warranty Disclaimer : The software is provided “as is” with no warranties of any kind. The licensor is not liable for any damages from using or being unable to use the software.

Other than these specific conditions, all terms of the Apache License 2.0 apply.

You can read it here: http://www.apache.org/licenses/LICENSE-2.0

Questions or clarifications?
Email <NAME_EMAIL>.
