import React from 'react';

const VisionSupportedIcon = ({ width, height, className }: any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H16.6667C18.5076 0 20 1.49238 20 3.33333V16.6667C20 18.5076 18.5076 20 16.6667 20H3.33333C1.49238 20 0 18.5076 0 16.6667V3.33333Z"
                fill="#BFF6DF"
            />
            <path
                d="M17.8983 9.78769C17.7553 9.59216 14.3496 5 9.9999 5C5.65018 5 2.24431 9.59216 2.10153 9.7875C2.03555 9.87789 2 9.98689 2 10.0988C2 10.2107 2.03555 10.3197 2.10153 10.4101C2.24431 10.6056 5.65018 15.1978 9.9999 15.1978C14.3496 15.1978 17.7553 10.6056 17.8983 10.4103C17.9643 10.3199 18 10.2109 18 10.099C18 9.98705 17.9643 9.87803 17.8983 9.78769ZM9.9999 14.1428C6.79587 14.1428 4.02084 11.0949 3.19937 10.0985C4.01978 9.10125 6.789 6.05494 9.9999 6.05494C13.2038 6.05494 15.9786 9.10231 16.8004 10.0992C15.98 11.0965 13.2108 14.1428 9.9999 14.1428Z"
                fill="#32996E"
            />
            <path
                d="M10.0008 6.93359C8.25572 6.93359 6.83594 8.35337 6.83594 10.0984C6.83594 11.8435 8.25572 13.2633 10.0008 13.2633C11.7458 13.2633 13.1656 11.8435 13.1656 10.0984C13.1656 8.35337 11.7458 6.93359 10.0008 6.93359ZM10.0008 12.2083C8.83734 12.2083 7.89091 11.2618 7.89091 10.0984C7.89091 8.93503 8.83738 7.98856 10.0008 7.98856C11.1642 7.98856 12.1107 8.93503 12.1107 10.0984C12.1107 11.2618 11.1642 12.2083 10.0008 12.2083Z"
                fill="#32996E"
            />
        </svg>
    );
};

export default VisionSupportedIcon;
