import React from 'react';

const CardIcon = ({height, width, className}:any) => {
    return (
        <svg className={className}  width={width} height={height} viewBox="0 0 36 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M33.1765 0H2.11765C0.950115 0 0 0.950115 0 2.11765V21.8824C0 23.0499 0.950115 24 2.11765 24H33.1765C34.344 24 35.2941 23.0499 35.2941 21.8824V2.11765C35.2941 0.950115 34.344 0 33.1765 0ZM33.8824 21.8824C33.8824 22.2706 33.5647 22.5882 33.1765 22.5882H2.11765C1.72803 22.5882 1.41176 22.2706 1.41176 21.8824V2.11765C1.41176 1.72796 1.72803 1.41177 2.11765 1.41177H33.1765C33.5647 1.41177 33.8824 1.72796 33.8824 2.11765V21.8824Z" />
            <path d="M0.706055 6.35303H34.5884V9.17656H0.706055V6.35303Z"/>
            <path d="M34.5882 5.64697H0.705882C0.316268 5.64697 0 5.96317 0 6.35286V9.17638C0 9.56607 0.316268 9.88227 0.705882 9.88227H34.5882C34.9779 9.88227 35.2941 9.56607 35.2941 9.17638V6.35286C35.2941 5.96317 34.9779 5.64697 34.5882 5.64697ZM33.8824 8.4705H1.41176V7.05874H33.8824V8.4705ZM19.0588 14.1176H6.35294C5.96333 14.1176 5.64706 14.4338 5.64706 14.8234C5.64706 15.2131 5.96333 15.5293 6.35294 15.5293H19.0588C19.4484 15.5293 19.7647 15.2131 19.7647 14.8234C19.7647 14.4338 19.4484 14.1176 19.0588 14.1176ZM13.4118 16.9411H6.35294C5.96333 16.9411 5.64706 17.2574 5.64706 17.647C5.64706 18.0366 5.96333 18.3529 6.35294 18.3529H13.4118C13.8014 18.3529 14.1176 18.0366 14.1176 17.647C14.1176 17.2574 13.8014 16.9411 13.4118 16.9411Z" />
        </svg>
    );
};

export default CardIcon;