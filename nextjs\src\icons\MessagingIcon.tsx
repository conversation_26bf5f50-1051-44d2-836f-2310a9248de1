import React from 'react';

const MessagingIcon = ({height, width, className}:any) => {
  return (
<svg className={className} width={width} height={height} viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M14.7044 3.34981L14.7038 3.34917C13.2044 1.79164 11.0522 0.9 8.79952 0.9C4.51564 0.9 0.9 4.09358 0.9 8.2C0.9 9.62219 1.33686 10.9861 2.16624 12.1667L1.013 15.7875C1.013 15.7875 1.013 15.7875 1.013 15.7875C0.971089 15.9191 0.969012 16.0601 1.00703 16.1928C1.04504 16.3255 1.12145 16.444 1.22665 16.5334C1.33184 16.6228 1.46113 16.6791 1.59824 16.6952C1.73534 16.7113 1.87415 16.6865 1.99721 16.6239L5.50864 14.8384C6.22373 15.1415 6.97999 15.3424 7.76219 15.4373C9.32625 17.067 11.4792 17.9 13.6792 17.9C14.8133 17.9 15.9434 17.6721 16.9699 17.2384L20.4815 19.024C20.4815 19.024 20.4815 19.024 20.4815 19.024C20.5798 19.074 20.6884 19.1 20.7987 19.1C21.271 19.1 21.6097 18.6398 21.4657 18.1875L20.3125 14.5667C22.2886 11.7528 21.9704 7.91724 19.2431 5.41701C18.0014 4.27876 16.4217 3.56754 14.7158 3.36213C14.712 3.35798 14.7082 3.35387 14.7044 3.34981ZM18.9853 14.007L19.0641 14.0686L18.9853 14.007C18.9151 14.0968 18.868 14.2023 18.8479 14.3145C18.8278 14.4266 18.8353 14.5419 18.8699 14.6505L18.9652 14.6201L18.8699 14.6505L19.6237 17.0172L17.3062 15.8388C17.2108 15.7903 17.1055 15.7643 16.9984 15.7629C16.8914 15.7614 16.7854 15.7846 16.6887 15.8305C15.767 16.2683 14.7263 16.5 13.6792 16.5C10.0862 16.5 7.1796 13.8444 7.1796 10.6C7.1796 7.35558 10.0862 4.7 13.6792 4.7C17.2722 4.7 20.1788 7.35558 20.1788 10.6C20.1788 11.829 19.7667 13.0069 18.9853 14.007ZM5.78998 13.4305C5.69328 13.3846 5.58732 13.3614 5.48028 13.3629L5.2178 13.528L5.17247 13.4389L5.17247 13.4389L2.85499 14.6173L3.6088 12.2505C3.64338 12.142 3.65095 12.0266 3.63083 11.9145C3.61072 11.8024 3.56355 11.6968 3.4934 11.6071C2.71204 10.6069 2.29992 9.42899 2.29992 8.2C2.29992 4.95558 5.20653 2.3 8.79952 2.3C10.1506 2.3 11.4538 2.68264 12.5387 3.37566C8.76853 3.87831 5.77968 6.87122 5.77968 10.6C5.77968 11.7026 6.0424 12.7671 6.53965 13.7333C6.28408 13.6472 6.03375 13.5461 5.78998 13.4305Z" strokeWidth="0.2"/>
    <path d="M11.2394 11.3C11.626 11.3 11.9394 10.9866 11.9394 10.6C11.9394 10.2134 11.626 9.9 11.2394 9.9C10.8528 9.9 10.5394 10.2134 10.5394 10.6C10.5394 10.9866 10.8528 11.3 11.2394 11.3Z" strokeWidth="0.2"/>
    <path d="M13.6393 11.3C14.0259 11.3 14.3393 10.9866 14.3393 10.6C14.3393 10.2134 14.0259 9.9 13.6393 9.9C13.2527 9.9 12.9393 10.2134 12.9393 10.6C12.9393 10.9866 13.2527 11.3 13.6393 11.3Z" strokeWidth="0.2"/>
    <path d="M16.0387 11.3C16.4253 11.3 16.7387 10.9866 16.7387 10.6C16.7387 10.2134 16.4253 9.9 16.0387 9.9C15.6521 9.9 15.3387 10.2134 15.3387 10.6C15.3387 10.9866 15.6521 11.3 16.0387 11.3Z" strokeWidth="0.2"/>
</svg>
  );
}

export default MessagingIcon;
