const AttachFileIcon = ({height, width, className}:any) => (
    <svg
        className={className}
        width={width}
        height={height}
        viewBox="0 0 10 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
    >
        <path d="M9.62791 13.186V5.65116C9.62791 5.30372 9.34744 5.02326 9 5.02326C8.65256 5.02326 8.37209 5.30372 8.37209 5.65116V13.186C8.37209 15.1493 6.77721 16.7442 4.81395 16.7442C2.8507 16.7442 1.25581 15.1493 1.25581 13.186V3.55814C1.25581 2.28977 2.28977 1.25581 3.55814 1.25581C4.82651 1.25581 5.86047 2.28977 5.86047 3.55814V12.5581C5.86047 12.8357 5.75021 13.1019 5.55395 13.2981C5.35769 13.4944 5.09151 13.6047 4.81395 13.6047C4.5364 13.6047 4.27022 13.4944 4.07396 13.2981C3.8777 13.1019 3.76744 12.8357 3.76744 12.5581V4.39535C3.76744 4.04791 3.48698 3.76744 3.13953 3.76744C2.79209 3.76744 2.51163 4.04791 2.51163 4.39535V12.5581C2.51163 13.8265 3.54558 14.8605 4.81395 14.8605C6.08233 14.8605 7.11628 13.8265 7.11628 12.5581V3.55814C7.11628 1.59488 5.5214 0 3.55814 0C1.59488 0 0 1.59488 0 3.55814V13.186C0 15.84 2.16 18 4.81395 18C7.46791 18 9.62791 15.84 9.62791 13.186Z" />
    </svg>
);

export default AttachFileIcon;
