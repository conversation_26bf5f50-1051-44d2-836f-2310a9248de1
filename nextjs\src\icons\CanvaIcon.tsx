const CanvaIcon = ({ className }) => {
    return (
        <svg
            className={className}
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.2592 0H1.72581C0.77263 0 0 0.77263 0 1.72581V17.2592C0 18.2124 0.77263 18.985 1.72581 18.985H17.2592C18.2124 18.985 18.985 18.2124 18.985 17.2592V1.72581C18.985 0.77263 18.2124 0 17.2592 0Z"
                fill="url(#paint0_linear_6651_242)"
            />
            <path
                opacity="0.07"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.139 12.9244C12.5013 13.5106 12.0871 13.914 11.1594 14.3878C10.628 14.653 10.03 14.8129 9.46612 14.8129C7.77447 14.8129 6.94924 13.2552 6.78496 11.91C6.36367 8.45838 9.39617 3.7147 11.5107 3.7147C12.0041 3.7147 12.4444 4.14358 12.5263 4.81211C12.6531 5.85258 12.6325 6.58997 11.5568 7.48839C11.4392 7.58652 11.4045 7.74539 11.4652 7.83973C11.5568 7.98016 11.8458 7.9948 12.4298 7.6993C13.5738 7.12186 13.9864 6.23049 13.8606 5.19869C13.7202 4.05357 12.7209 3.09497 11.3812 3.09497C10.7294 3.09497 10.0132 3.28962 9.37286 3.67946C6.58434 5.37816 4.7848 9.25486 5.14644 12.2109C5.26084 13.1489 5.65068 14.1758 6.38753 14.8661C6.88418 15.3258 7.78151 15.8924 8.83988 15.8924C9.98554 15.8924 10.9528 15.45 11.8203 14.9024C12.4086 14.5256 12.9004 14.0701 13.3157 13.6038C13.9978 12.9694 13.5076 12.5764 13.139 12.9244Z"
                stroke="#1D1D1B"
                strokeMiterlimit="10"
            />
            <path
                opacity="0.05"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.3817 3.09395C12.7221 3.09395 13.7213 4.05256 13.8612 5.19767C13.987 6.23002 13.5749 7.12139 12.4304 7.69828C12.0947 7.86799 11.8562 7.93577 11.6968 7.93577C11.5791 7.93577 11.5043 7.8989 11.4652 7.83926C11.4045 7.74546 11.4387 7.58605 11.5569 7.48791C12.6326 6.58895 12.6532 5.85156 12.5263 4.81163C12.4445 4.1431 12.0036 3.71423 11.5108 3.71423C9.39623 3.71423 6.36318 8.45844 6.78501 11.9095C6.94929 13.2547 7.77452 14.8124 9.46617 14.8124C10.0301 14.8124 10.6276 14.653 11.1594 14.3874C12.0871 13.9129 12.5014 13.5101 13.139 12.924C13.2339 12.8345 13.3364 12.7944 13.4253 12.7944C13.6823 12.7944 13.8227 13.1322 13.3163 13.6028C12.901 14.0691 12.4087 14.5251 11.8209 14.9014C10.9534 15.449 9.98559 15.8914 8.84048 15.8914C7.78265 15.8914 6.88477 15.3248 6.38812 14.865C5.65182 14.1748 5.26144 13.1479 5.14704 12.2099C4.78539 9.2533 6.58494 5.37714 9.37345 3.67844C10.0132 3.2886 10.73 3.09395 11.3817 3.09395ZM11.3817 2.55176C10.6075 2.55176 9.7942 2.78707 9.09151 3.21486C6.22275 4.96236 4.21175 9.02721 4.60918 12.2755C4.75503 13.47 5.26795 14.5582 6.01726 15.2608C6.39951 15.6149 7.43565 16.4342 8.83993 16.4342C10.2464 16.4342 11.3796 15.8215 12.1099 15.3606C12.6798 14.9957 13.2003 14.5451 13.7045 13.9823C14.315 13.4027 14.2153 12.9169 14.1437 12.7337C14.0288 12.4414 13.7468 12.2527 13.4253 12.2527C13.1905 12.2527 12.9568 12.3514 12.7671 12.5303L12.6722 12.6176C12.1018 13.143 11.7255 13.49 10.9127 13.9053C10.4421 14.1401 9.92649 14.2708 9.46617 14.2708C8.10309 14.2708 7.46004 12.9636 7.32341 11.8445C7.05719 9.66646 8.27659 6.99235 9.61907 5.47636C10.3049 4.70157 10.9946 4.25697 11.5113 4.25697C11.7109 4.25697 11.9386 4.47005 11.989 4.87778C12.1078 5.85156 12.0698 6.35364 11.2099 7.07205C10.8818 7.34477 10.7956 7.8013 11.0109 8.13367C11.0949 8.26325 11.295 8.47796 11.6973 8.47796C11.9619 8.47796 12.2818 8.38145 12.6754 8.18192C13.949 7.53942 14.5612 6.45611 14.3996 5.13153C14.2196 3.66109 12.9221 2.55176 11.3817 2.55176Z"
                fill="#1D1D1B"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.139 12.9244C12.5013 13.5106 12.0871 13.914 11.1594 14.3878C10.628 14.653 10.03 14.8129 9.46612 14.8129C7.77447 14.8129 6.94924 13.2552 6.78496 11.91C6.36367 8.45838 9.39617 3.7147 11.5107 3.7147C12.0041 3.7147 12.4444 4.14358 12.5263 4.81211C12.6531 5.85258 12.6325 6.58997 11.5568 7.48839C11.4392 7.58652 11.4045 7.74539 11.4652 7.83973C11.5568 7.98016 11.8458 7.9948 12.4298 7.6993C13.5738 7.12186 13.9864 6.23049 13.8606 5.19869C13.7202 4.05357 12.7209 3.09497 11.3812 3.09497C10.7294 3.09497 10.0132 3.28962 9.37286 3.67946C6.58434 5.37816 4.7848 9.25486 5.14644 12.2109C5.26084 13.1489 5.65068 14.1758 6.38753 14.8661C6.88418 15.3258 7.78151 15.8924 8.83988 15.8924C9.98554 15.8924 10.9528 15.45 11.8203 14.9024C12.4086 14.5256 12.9004 14.0701 13.3157 13.6038C13.9978 12.9694 13.5076 12.5764 13.139 12.9244Z"
                fill="white"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_6651_242"
                    x1="18.5469"
                    y1="19.4784"
                    x2="0.700518"
                    y2="-0.204407"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#823AF3" />
                    <stop offset="0.36" stopColor="#4B66E1" />
                    <stop offset="0.906" stopColor="#01F1C4" />
                </linearGradient>
            </defs>
        </svg>
    );
};

export default CanvaIcon;
