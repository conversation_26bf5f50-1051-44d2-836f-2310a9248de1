{"name": "go-custom-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "dev:dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "clean-build": "npm cache clean --force && rm -rf .next && npm run build"}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@material-ui/core": "^4.12.4", "@mui/material": "^5.15.21", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@reduxjs/toolkit": "^2.2.5", "@sentry/nextjs": "^9.11.0", "@tanstack/react-table": "^8.17.3", "axios": "^1.7.2", "chart.js": "^4.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "embla-carousel-react": "^8.6.0", "firebase": "^9.23.0", "formik": "^2.4.6", "iron-session": "^8.0.2", "json-to-csv-export": "^3.1.0", "lucide-react": "^0.378.0", "material-ui-chip-input": "^1.1.0", "moment": "^2.30.1", "next": "14.1.0", "next-themes": "^0.3.0", "rc-steps": "^6.0.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-emoji-picker": "^1.0.13", "react-google-recaptcha": "^3.1.0", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-mentions": "^4.4.10", "react-redux": "^9.1.2", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "react-table": "^7.8.0", "remark-gfm": "^4.0.0", "sharp": "^0.33.4", "socket.io-client": "^4.7.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.4.0"}, "devDependencies": {"@babel/plugin-transform-optional-chaining": "^7.25.9", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@builder.io/react-hydration-overlay": "^0.0.8", "@tailwindcss/typography": "^0.5.13", "autoprefixer": "^10.4.19", "babel-loader": "^9.2.1", "eslint": "^8.57.0", "eslint-config-next": "14.1.3", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "postcss-nesting": "^12.1.5", "tailwindcss": "^3.4.4", "typescript": "^5.7.2"}}