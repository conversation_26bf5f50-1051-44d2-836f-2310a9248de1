services:
  redis:
    image: "redis/redis-stack:latest" # Image name local developement with dashboard
    container_name: redis_container # Container name
    volumes:
      - redis_data:/data
    networks:
      - app-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 3s
      timeout: 5s
      retries: 5
  app:
    build:
      context: ./nextjs
      dockerfile: Dockerfile
      target: development
      args:
        - NEXT_PUBLIC_COOKIE_NAME=${NEXT_PUBLIC_COOKIE_NAME}
        - NEXT_PUBLIC_COOKIE_PASSWORD=${NEXT_PUBLIC_COOKIE_PASSWORD}
        - NEXT_PUBLIC_COMMON_NODE_API_URL=${NEXT_PUBLIC_COMMON_NODE_API_URL}
        - NEXT_PUBLIC_APP_ENVIRONMENT=${NEXT_PUBLIC_APP_ENVIRONMENT}
        - NEXT_PUBLIC_HTTPS_PROTOCOL=${NEXT_PUBLIC_HTTPS_PROTOCOL}
        - NEXT_PUBLIC_IMAGE_DOMAIN=${NEXT_PUBLIC_IMAGE_DOMAIN}
        - NEXT_PUBLIC_AWS_S3_URL=${NEXT_PUBLIC_AWS_S3_URL}
        - NEXT_PUBLIC_DOMAIN_URL=${NEXT_PUBLIC_DOMAIN_URL}
        - NEXT_PUBLIC_API_PREFIX=${NEXT_PUBLIC_API_PREFIX}
        - NEXT_PUBLIC_PYTHON_API_URL=${NEXT_PUBLIC_PYTHON_API_URL}
        - NEXT_PUBLIC_SERVER_NODE_API_URL=${NEXT_PUBLIC_SERVER_NODE_API_URL}
        - NEXT_PUBLIC_SOCKET_CONNECTION_URL=${NEXT_PUBLIC_SOCKET_CONNECTION_URL}
        - NEXT_PUBLIC_SECURITY_KEY=${NEXT_PUBLIC_SECURITY_KEY}
        - NEXT_PUBLIC_MESSAGE_LIMIT=${NEXT_PUBLIC_MESSAGE_LIMIT}
        - NEXT_PUBLIC_FREE_TRIAL_DAYS=${NEXT_PUBLIC_FREE_TRIAL_DAYS}
        - NEXT_PUBLIC_OPENAI_PLATFORM_URL=${NEXT_PUBLIC_OPENAI_PLATFORM_URL}
        - NEXT_PUBLIC_FRESHDESK_SUPPORT_URL=${FRESHDESK_SUPPORT_URL}
        - CSRF_TOKEN_SECRET=${CSRF_TOKEN_SECRET}
    image: weamai-app:latest
    container_name: weam-frontend-container
    ports:
      - "3000:3000"
    restart: always
    env_file:
      - .env  # Load environment variables from a .env file
    volumes:
      - ./nextjs:/usr/src/app
      - /usr/src/app/node_modules
      - /usr/src/app/.next
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - app-network
    depends_on:
      - nodejs
      - mongo
    
  
  nodejs:
    build:
      context: ./nodejs
      dockerfile: Dockerfile  # Use the Dockerfile in the current directory
    container_name: node_app
    ports:
      - "4050:4050"
    env_file:
      - .env  # Load environment variables from a .env file
    volumes:
      - ./nodejs:/usr/src/app        
      - /usr/src/app/node_modules 
      - ./nodejs/storage:/usr/src/app/storage
    networks:
      - app-network
    depends_on:
      - mongo
      - minio
  
  pybase_docker:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: BaseDockerfile
    image: pybase_image
    # image: pybase_image:latest
    # container_name: pybase_container


  celery_service:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: DockerfileRed
    image: "celery_app" # Custom image name
    container_name: celery_service # Container name
    depends_on:
      - pybase_docker
      - redis
      - mongo
    networks:
      - app-network
    entrypoint: /usr/local/bin/celery_entrypoint.sh
    volumes:
      - .:/app
    


  extraction_worker:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: src/celery_worker_hub/extraction/Dockerfile
    image: "extraction_worker" # Custom image name
    container_name: extraction_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
      - mongo
    networks:
      - app-network
    entrypoint: /usr/local/bin/extraction_worker.sh
    volumes:
      - .:/app
  

  scrapper_worker:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: src/celery_worker_hub/web_scraper/Dockerfile
    image: "scrapper_worker" # Custom image name
    container_name: scrapper_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
      - mongo
    networks:
      - app-network
    entrypoint: /usr/local/bin/scraper_worker.sh
    volumes:
      - .:/app
   
  import_worker:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: src/celery_worker_hub/import_worker/Dockerfile
    image: "import_worker" # Custom image name
    container_name: import_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
      - mongo
    networks:
      - app-network
    entrypoint: /usr/local/bin/import_chat_worker.sh
    volumes:
      - .:/app
   

  
  gateway:
    env_file: ${ENV_FILE}
    build:
      context: ./ai-python
      dockerfile: Dockerfile
    image: "gateway_app" # Custom image name
    container_name: gateway_service # Container name
    ports:
      - "${GATEWAY_PORT}:${GATEWAY_PORT}"
    depends_on:
      - pybase_docker
      - redis
      - celery_service
      - extraction_worker
      - qdrant_primary
      - mongo
    networks:
      - app-network
    command: uvicorn src.gateway.web:app --host 0.0.0.0 --port ${GATEWAY_PORT} --reload --workers 2
    volumes:
      - .:/app
  mcp:
   env_file: ${ENV_FILE}
   build:
    context: ./ai-python
    dockerfile: src/MCP/DockerFile
   image: "mcp_server"
   container_name: mcp_server
   ports:
      - "${MCP_PORT}:${MCP_PORT}"
   networks:
      - app-network
   depends_on:
      - redis
      - mongo
   volumes:
      - .:/app
   healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # wdb:
  #   image: kozea/wdb:3.3.0
  #   container_name: web-wdb
  #   networks:
  #     - app-network
  #   ports:
  #     - ${WDB_PORT}:${WDB_PORT}
  #   depends_on:
  #     - gateway
  
  qdrant_primary:
    image: "qdrant/qdrant:latest"
    container_name: qdrant_primary  # 👈 Add this to both nodes
    ports:
      - "${QDRANT_DASHBOARD_PORT}:${QDRANT_DASHBOARD_PORT}"
    environment:
      QDRANT__CLUSTER__ENABLED: true
      QDRANT__LOG_LEVEL: INFO
    command: "./qdrant --uri http://qdrant_primary:${QDRANT_NODE_PORT}"
    volumes:
      - ./data/qdrant_primary_data:/qdrant/storage
    restart: always
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - app-network


  qdrant_secondary:
    image: "qdrant/qdrant:latest"
    container_name: qdrant_secondary
    depends_on:
      - qdrant_primary
    environment:
      QDRANT__CLUSTER__ENABLED: true
      QDRANT__LOG_LEVEL: INFO
    command: "./qdrant --bootstrap http://qdrant_primary:${QDRANT_NODE_PORT} --uri http://qdrant_secondary:${QDRANT_NODE_PORT}"
    volumes:
      - ./data/qdrant_secondary_data:/qdrant/storage
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s  # Increased interval
      timeout: 5s  # Increased timeout
      retries: 10   # Increased retries
    restart: always
    networks:
      - app-network
    
  mongo:
    image: mongo:latest
    container_name: weamai-mongo-1
    ports:
      - "${MONGO_PORT}:${MONGO_PORT}"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=test
    volumes:
      - mongo-data:/data/db
    networks:
      - app-network

  # mongo-express:
  #   image: mongo-express:latest
  #   restart: always
  #   ports:
  #     - "8081:8081"
  #   environment:
  #     - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
  #     - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
  #     - ME_CONFIG_MONGODB_ADMINPASSWORD=password
  #     - ME_CONFIG_BASICAUTH_USERNAME=weam
  #     - ME_CONFIG_BASICAUTH_PASSWORD=password
  #     - ME_CONFIG_MONGODB_SERVER=mongo
  #   networks:
  #     - app-network
  #   depends_on:
  #     - mongo




  
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "${MINIO_PORT}:${MINIO_PORT}"     # S3 API port
      - "${MINIO_DASHBOARD_PORT}:${MINIO_DASHBOARD_PORT}"     # Web UI port
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=${AWS_ACCESS_KEY_ID}
      - MINIO_ROOT_PASSWORD=${AWS_SECRET_ACCESS_KEY}
    command: server /data --console-address ":${MINIO_DASHBOARD_PORT}"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${MINIO_PORT}/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - app-network
  
  
  minio-init:
    image: minio/mc
    env_file: ${ENV_FILE}
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: ["/bin/sh", "/minio.sh"]
    volumes:
      - ./ai-python/minio.sh:/minio.sh:ro
    networks:
      - app-network

  
  
  
    
networks:
  app-network:
    driver: bridge



volumes:
  mongo-data:
  redis_data:
  minio_data:
  app:
  db:
  data:
  localstack:


