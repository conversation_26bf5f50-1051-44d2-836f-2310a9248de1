import React from 'react';

const PromptEnhanceIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 480 480"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M315.165 123.621C319.839 122.538 324.622 121.994 329.42 121.998C357.881 121.998 382.951 141.189 390.318 168.397L390.549 169.316C390.649 169.701 390.751 170.09 390.842 170.476L390.968 171.042C391.863 174.918 394.254 178.282 397.621 180.401C400.987 182.521 405.055 183.222 408.936 182.354C412.818 181.484 416.198 179.113 418.338 175.76C420.478 172.406 421.203 168.342 420.357 164.455L420.215 163.82C412.827 130.687 432.582 97.901 465.497 89.0043C466.154 88.8205 466.825 88.6579 467.485 88.4923L468.003 88.3628C471.857 87.3982 475.174 84.9493 477.231 81.5499C479.288 78.1504 479.918 74.0758 478.984 70.2139C478.047 66.3539 475.623 63.02 472.241 60.9372C468.859 58.8544 464.791 58.191 460.922 59.0914C456.248 60.1747 451.466 60.7194 446.668 60.7148C418.228 60.7148 393.167 41.5238 385.776 14.2191L385.023 11.2977C384.031 7.46361 381.571 4.17385 378.173 2.13981C374.775 0.10577 370.713 -0.509065 366.865 0.428259C363.017 1.36901 359.695 3.78776 357.619 7.16054C355.542 10.5333 354.878 14.5885 355.77 18.4476C363.462 51.7246 343.68 84.7697 310.593 93.7117C309.937 93.8954 309.265 94.058 308.605 94.2236L308.087 94.3532C304.233 95.3177 300.916 97.7666 298.859 101.166C296.802 104.566 296.172 108.64 297.106 112.502C298.044 116.362 300.468 119.695 303.85 121.778C307.232 123.86 311.299 124.524 315.168 123.624L315.165 123.621ZM381.981 64.6692C391.343 73.687 402.477 80.6602 414.677 85.1462C405.592 94.5437 398.589 105.75 394.124 118.035C384.76 109.02 373.624 102.049 361.423 97.5667C370.513 88.1686 377.518 76.9587 381.981 64.6692ZM68.2003 123.621C72.8743 122.538 77.657 121.994 82.455 121.998C110.916 121.998 135.986 141.189 143.353 168.397L143.585 169.316C143.684 169.701 143.787 170.09 143.877 170.476L144.003 171.042C144.898 174.918 147.29 178.282 150.656 180.401C154.022 182.521 158.09 183.222 161.972 182.354C165.853 181.484 169.233 179.113 171.373 175.76C173.513 172.406 174.239 168.342 173.392 164.455L173.251 163.82C165.863 130.687 185.617 97.901 218.533 89.0043C219.189 88.8205 219.861 88.6579 220.52 88.4923L221.038 88.3628C224.893 87.3982 228.21 84.9493 230.267 81.5499C232.323 78.1504 232.953 74.0758 232.019 70.2139C231.081 66.3543 228.658 63.0209 225.276 60.9382C221.894 58.8555 217.826 58.1918 213.958 59.0914C209.284 60.1747 204.501 60.7194 199.703 60.7148C171.263 60.7148 146.202 41.5238 138.811 14.2191L138.058 11.2977C137.067 7.46361 134.606 4.17385 131.208 2.13981C127.81 0.10577 123.748 -0.509065 119.9 0.428259C116.053 1.36901 112.731 3.78776 110.654 7.16054C108.577 10.5333 107.913 14.5885 108.805 18.4476C116.497 51.7246 96.7157 84.7697 63.6284 93.7117C62.9719 93.8954 62.3002 94.058 61.6407 94.2236L61.1226 94.3532C57.2682 95.3177 53.9512 97.7666 51.8944 101.166C49.8375 104.566 49.2076 108.64 50.1417 112.502C51.0797 116.362 53.5031 119.695 56.8852 121.778C60.2672 123.86 64.3346 124.524 68.2033 123.624L68.2003 123.621ZM135.016 64.6692C144.378 73.687 155.512 80.6602 167.712 85.1462C158.627 94.5437 151.624 105.75 147.16 118.035C137.795 109.02 126.659 102.049 114.458 97.5667C123.548 88.1686 130.553 76.9587 135.016 64.6692ZM460.922 303.038C456.248 304.122 451.466 304.666 446.668 304.662C418.228 304.662 393.167 285.471 385.776 258.166L385.023 255.245C384.031 251.411 381.571 248.121 378.173 246.087C374.775 244.053 370.713 243.438 366.865 244.375C363.017 245.316 359.695 247.735 357.619 251.107C355.542 254.48 354.878 258.535 355.77 262.395C363.462 295.672 343.68 328.717 310.593 337.659C309.937 337.842 309.265 338.005 308.605 338.171L308.087 338.3C304.233 339.265 300.916 341.713 298.859 345.113C296.802 348.512 296.172 352.587 297.106 356.449C298.044 360.309 300.468 363.642 303.85 365.725C307.232 367.807 311.299 368.471 315.168 367.571C319.842 366.488 324.625 365.943 329.423 365.948C357.884 365.948 382.954 385.139 390.321 412.347L390.552 413.266C390.652 413.651 390.754 414.04 390.845 414.425L390.971 414.992C391.866 418.867 394.257 422.232 397.624 424.351C400.99 426.47 405.058 427.172 408.939 426.304C412.821 425.434 416.201 423.063 418.341 419.71C420.481 416.356 421.207 412.292 420.36 408.405L420.218 407.769C412.831 374.637 432.585 341.851 465.5 332.954C466.157 332.77 466.828 332.608 467.488 332.442L468.006 332.313C471.847 331.336 475.148 328.884 477.193 325.489C479.238 322.094 479.862 318.03 478.93 314.178C477.998 310.326 475.586 306.997 472.215 304.912C468.845 302.827 464.788 302.155 460.925 303.041L460.922 303.038ZM394.127 361.985C384.763 352.969 373.627 345.999 361.426 341.517C370.516 332.119 377.521 320.909 381.984 308.619C391.346 317.637 402.48 324.61 414.68 329.096C405.595 338.494 398.592 349.7 394.127 361.985ZM328.357 216.772C338.045 207.074 338.045 191.299 328.357 181.61L297.679 150.905C292.983 146.207 286.74 143.62 280.096 143.62C273.452 143.62 267.212 146.207 262.51 150.908L210.828 202.632C210.786 202.674 210.759 202.725 210.717 202.768L7.26325 406.387C-2.42259 416.085 -2.41958 431.858 7.26325 441.546L37.926 472.23C42.6244 476.935 48.8678 479.522 55.5117 479.522C62.1557 479.522 68.3991 476.935 73.0944 472.233L276.669 268.496L328.351 216.772H328.357ZM280.096 175.918L303.347 199.19L266.019 236.548L242.768 213.276L280.096 175.918ZM55.5117 447.224L32.2729 423.964L221.49 234.593L244.729 257.853L55.5117 447.224Z"
               
            />
            
        </svg>
    );
};

export default PromptEnhanceIcon;
