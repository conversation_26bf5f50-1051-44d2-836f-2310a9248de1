import React from 'react';

const StorageIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12.1055 6.5C10.8775 6.5 0.105469 6.416 0.105469 3.5C0.105469 0.584 10.8775 0.5 12.1055 0.5C13.3335 0.5 24.1055 0.584 24.1055 3.5C24.1055 6.416 13.3335 6.5 12.1055 6.5ZM1.69347 3.5C2.42547 4.068 5.93847 5 12.1055 5C18.2725 5 21.7855 4.068 22.5175 3.5C21.7855 2.932 18.2725 2 12.1055 2C5.93847 2 2.42547 2.932 1.69347 3.5ZM12.1055 12.5C10.8775 12.5 0.105469 12.416 0.105469 9.5C0.105585 9.30862 0.178856 9.12453 0.310279 8.98541C0.441703 8.8463 0.621335 8.76269 0.812396 8.75169C1.00346 8.7407 1.19149 8.80317 1.338 8.92629C1.48451 9.04942 1.57841 9.2239 1.60047 9.414C2.06247 9.967 5.61247 11 12.1055 11C18.5985 11 22.1485 9.967 22.6105 9.414C22.6325 9.2239 22.7264 9.04942 22.8729 8.92629C23.0194 8.80317 23.2075 8.7407 23.3985 8.75169C23.5896 8.76269 23.7692 8.8463 23.9007 8.98541C24.0321 9.12453 24.1054 9.30862 24.1055 9.5C24.1055 12.416 13.3335 12.5 12.1055 12.5ZM22.6055 9.499C22.6055 9.5 22.6055 9.5 22.6055 9.499V9.499ZM1.60547 9.499C1.60547 9.5 1.60547 9.5 1.60547 9.499V9.499ZM12.1055 18.5C10.8775 18.5 0.105469 18.416 0.105469 15.5C0.105585 15.3086 0.178856 15.1245 0.310279 14.9854C0.441703 14.8463 0.621335 14.7627 0.812396 14.7517C1.00346 14.7407 1.19149 14.8032 1.338 14.9263C1.48451 15.0494 1.57841 15.2239 1.60047 15.414C2.06247 15.967 5.61247 17 12.1055 17C18.5985 17 22.1485 15.967 22.6105 15.414C22.6325 15.2239 22.7264 15.0494 22.8729 14.9263C23.0194 14.8032 23.2075 14.7407 23.3985 14.7517C23.5896 14.7627 23.7692 14.8463 23.9007 14.9854C24.0321 15.1245 24.1054 15.3086 24.1055 15.5C24.1055 18.416 13.3335 18.5 12.1055 18.5ZM22.6055 15.499C22.6055 15.5 22.6055 15.5 22.6055 15.499V15.499ZM1.60547 15.499C1.60547 15.5 1.60547 15.5 1.60547 15.499V15.499Z"/>
      <path d="M12.1055 24.5C10.8775 24.5 0.105469 24.416 0.105469 21.5V3.5C0.105469 3.30109 0.184486 3.11032 0.325139 2.96967C0.465791 2.82902 0.656556 2.75 0.855469 2.75C1.05438 2.75 1.24515 2.82902 1.3858 2.96967C1.52645 3.11032 1.60547 3.30109 1.60547 3.5V21.419C2.08647 21.975 5.63547 23 12.1055 23C18.5755 23 22.1245 21.975 22.6055 21.419V3.5C22.6055 3.30109 22.6845 3.11032 22.8251 2.96967C22.9658 2.82902 23.1566 2.75 23.3555 2.75C23.5544 2.75 23.7451 2.82902 23.8858 2.96967C24.0265 3.11032 24.1055 3.30109 24.1055 3.5V21.5C24.1055 24.416 13.3335 24.5 12.1055 24.5Z"/>
      <path d="M5.10547 15.5C5.65775 15.5 6.10547 15.0523 6.10547 14.5C6.10547 13.9477 5.65775 13.5 5.10547 13.5C4.55318 13.5 4.10547 13.9477 4.10547 14.5C4.10547 15.0523 4.55318 15.5 5.10547 15.5Z"/>
      <path d="M5.10547 9.5C5.65775 9.5 6.10547 9.05228 6.10547 8.5C6.10547 7.94772 5.65775 7.5 5.10547 7.5C4.55318 7.5 4.10547 7.94772 4.10547 8.5C4.10547 9.05228 4.55318 9.5 5.10547 9.5Z"/>
      <path d="M5.10547 21.5C5.65775 21.5 6.10547 21.0523 6.10547 20.5C6.10547 19.9477 5.65775 19.5 5.10547 19.5C4.55318 19.5 4.10547 19.9477 4.10547 20.5C4.10547 21.0523 4.55318 21.5 5.10547 21.5Z"/>
    </svg>
    
  );
}

export default StorageIcon;
