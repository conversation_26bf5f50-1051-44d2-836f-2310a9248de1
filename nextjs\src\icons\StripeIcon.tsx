const StripeIcon = ({ className }) => {
    return (
        <svg
            className={className}
            width="26"
            height="26"
            viewBox="0 0 26 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_6651_185)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M11.5387 8.62268C11.5387 7.81315 12.2497 7.49997 13.3986 7.49997C15.3001 7.53889 17.166 7.99628 18.8487 8.836V3.94925C17.1135 3.30322 15.2629 2.98118 13.3986 3.00085C8.97017 3.00085 6 5.19841 6 8.86791C6 14.6091 14.3217 13.6767 14.3217 16.1514C14.3217 17.1192 13.4485 17.4194 12.2198 17.4194C10.4079 17.4194 8.06764 16.7092 6.23203 15.7637V20.7143C8.12191 21.4933 10.1594 21.8998 12.2204 21.9091C16.7717 21.9091 19.909 19.7789 19.909 16.0456C19.909 9.84939 11.5387 10.9561 11.5387 8.62386V8.62268Z"
                    fill="#6772E5"
                />
            </g>
            <defs>
                <clipPath id="clip0_6651_185">
                    <rect width="26" height="26" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default StripeIcon;
