import React from 'react';

const DarkTheme = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
     <path d="M15.6765 9.10563C15.5123 9.06459 15.3482 9.10563 15.2046 9.20822C14.6711 9.65964 14.0555 10.029 13.3784 10.2752C12.7423 10.5214 12.0446 10.6446 11.306 10.6446C9.64391 10.6446 8.12551 9.96742 7.03801 8.87992C5.9505 7.79241 5.27338 6.27401 5.27338 4.61197C5.27338 3.91433 5.39649 3.2372 5.60168 2.62163C5.82739 1.96503 6.15569 1.36998 6.58659 0.857004C6.77126 0.631295 6.73022 0.302991 6.50451 0.118321C6.36088 0.0157259 6.19673 -0.0253121 6.03258 0.0157259C4.28846 0.487662 2.77006 1.53413 1.68256 2.92942C0.636088 4.30419 0 6.00726 0 7.87449C0 10.1111 0.902835 12.1424 2.3802 13.6198C3.85757 15.0972 5.88894 16 8.12551 16C10.0133 16 11.7574 15.3434 13.1527 14.2559C14.5685 13.1479 15.5944 11.5679 16.0253 9.76223C16.1279 9.45445 15.9638 9.16718 15.6765 9.10563ZM12.5371 13.3941C11.347 14.338 9.82859 14.9125 8.16655 14.9125C6.21725 14.9125 4.45262 14.1123 3.18044 12.8401C1.90826 11.5679 1.10802 9.80327 1.10802 7.85397C1.10802 6.23297 1.64152 4.75561 2.56487 3.56551C3.20096 2.74475 4.0012 2.06762 4.92455 1.59569C4.82196 1.82139 4.71936 2.0471 4.63729 2.29333C4.37054 3.03201 4.24743 3.81173 4.24743 4.63249C4.24743 6.58179 5.04767 8.36695 6.31984 9.63912C7.59202 10.9113 9.37717 11.7115 11.3265 11.7115C12.1883 11.7115 13.009 11.5679 13.7682 11.2806C14.035 11.178 14.3017 11.0754 14.5479 10.9523C14.0555 11.8962 13.3784 12.7375 12.5371 13.3941Z" />
    </svg>
  );
}

export default DarkTheme;




