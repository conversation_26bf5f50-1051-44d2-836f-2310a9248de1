import React from 'react'
import ApiKeyInput from './ApiKeyInput';

const GeminiModelProvider = () => {
    return (
        <>
            {/* <ApiKeyInput labelName={"Google Gemini API key"} imgSrc={GooglePalmIcon} imgAlt={"Google Gemini API key"} inputId={"google-gemini-api-key"} labelhtmlFor={"google-gemini-api-key"} className={"mb-4"} /> */}
        </>
      );
}

export default GeminiModelProvider