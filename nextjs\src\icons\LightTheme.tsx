import React from 'react';

const LightTheme = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.836 5.16402C10.1164 4.44444 9.10053 3.97884 8 3.97884C6.89947 3.97884 5.8836 4.42328 5.16402 5.16402C4.44444 5.8836 3.97884 6.89947 3.97884 8C3.97884 9.10053 4.44444 10.1164 5.16402 10.836C5.8836 11.5556 6.89947 12.0212 8 12.0212C9.10053 12.0212 10.1164 11.5767 10.836 10.836C11.5556 10.1164 12.0212 9.10053 12.0212 8C12.0212 6.89947 11.5767 5.8836 10.836 5.16402ZM10.0741 10.0741C9.54497 10.6032 8.80423 10.9206 8 10.9206C7.19577 10.9206 6.45503 10.6032 5.92593 10.0741C5.39683 9.54497 5.07936 8.80423 5.07936 8C5.07936 7.19577 5.39683 6.45503 5.92593 5.92593C6.45503 5.39683 7.19577 5.07936 8 5.07936C8.80423 5.07936 9.54497 5.39683 10.0741 5.92593C10.6032 6.45503 10.9206 7.19577 10.9206 8C10.9206 8.80423 10.6032 9.54497 10.0741 10.0741ZM15.4497 7.44974H13.8201C13.5238 7.44974 13.2698 7.7037 13.2698 8C13.2698 8.2963 13.5238 8.55026 13.8201 8.55026H15.4497C15.746 8.55026 16 8.2963 16 8C16 7.7037 15.746 7.44974 15.4497 7.44974ZM8 13.2698C7.7037 13.2698 7.44974 13.5238 7.44974 13.8201V15.4497C7.44974 15.746 7.7037 16 8 16C8.2963 16 8.55026 15.746 8.55026 15.4497V13.8201C8.55026 13.5238 8.2963 13.2698 8 13.2698ZM13.6508 12.8889L12.4868 11.7249C12.2963 11.5132 11.9365 11.5132 11.7249 11.7249C11.5132 11.9365 11.5132 12.2751 11.7249 12.4868L12.8889 13.6508C13.1005 13.8624 13.4392 13.8624 13.6508 13.6508C13.8624 13.4392 13.8624 13.1005 13.6508 12.8889ZM8 0C7.7037 0 7.44974 0.253968 7.44974 0.550265V2.17989C7.44974 2.47619 7.7037 2.73016 8 2.73016C8.2963 2.73016 8.55026 2.47619 8.55026 2.17989V0.550265C8.55026 0.253968 8.2963 0 8 0ZM13.672 2.34921C13.4603 2.13757 13.1217 2.13757 12.9101 2.34921L11.746 3.51323C11.5344 3.72487 11.5344 4.06349 11.746 4.27513C11.9365 4.48677 12.2963 4.48677 12.5079 4.27513L13.672 3.11111C13.8836 2.89947 13.8836 2.56085 13.672 2.34921ZM2.17989 7.44974H0.550265C0.253968 7.44974 0 7.7037 0 8C0 8.2963 0.232804 8.55026 0.550265 8.55026H2.17989C2.47619 8.55026 2.73016 8.2963 2.73016 8C2.73016 7.7037 2.47619 7.44974 2.17989 7.44974ZM4.25397 11.7249C4.06349 11.5132 3.7037 11.5132 3.49206 11.7249L2.32804 12.8889C2.1164 13.1005 2.1164 13.4392 2.32804 13.6508C2.53968 13.8624 2.87831 13.8624 3.08995 13.6508L4.25397 12.4868C4.46561 12.2751 4.46561 11.9365 4.25397 11.7249ZM4.25397 3.51323L3.08995 2.34921C2.87831 2.13757 2.53968 2.13757 2.32804 2.34921C2.1164 2.56085 2.1164 2.89947 2.32804 3.11111L3.49206 4.27513C3.7037 4.48677 4.04233 4.48677 4.25397 4.27513C4.46561 4.06349 4.46561 3.72487 4.25397 3.51323Z" />
    </svg>
  );
}

export default LightTheme;