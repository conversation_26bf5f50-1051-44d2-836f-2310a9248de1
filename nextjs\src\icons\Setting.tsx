import React from 'react';
const Setting = ({height, width, className}:any) => {
    return (
            <svg
                className={className} width={width} height={height}
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path d="M11.6949 20H8.30473C7.51361 20 6.84461 19.4508 6.71444 18.6945L6.53147 17.6316C6.47183 17.2848 6.25742 16.9852 5.94348 16.8094C5.62953 16.6336 5.25433 16.6035 4.91459 16.727L3.8736 17.1051C3.13287 17.3742 2.3075 17.0875 1.91214 16.423L0.217474 13.5773C-0.178285 12.9133 -0.0219158 12.0773 0.589051 11.5898L1.44747 10.9047C1.72756 10.6813 1.88796 10.3516 1.88796 10C1.88796 9.64844 1.72756 9.31875 1.44747 9.09531L0.589051 8.41055C-0.0219158 7.92305 -0.177882 7.08711 0.217474 6.42305L1.91254 3.57734C2.3083 2.91328 3.13327 2.62617 3.87401 2.89531L4.91499 3.27344C5.25473 3.39688 5.62953 3.3668 5.94388 3.19102C6.25783 3.01523 6.47223 2.71563 6.53188 2.36875L6.71484 1.30586C6.84502 0.549219 7.51361 0 8.30513 0H11.6953C12.4864 0 13.1554 0.549219 13.2852 1.30547L13.4681 2.36836C13.5278 2.71523 13.7422 3.01484 14.0561 3.19062C14.3701 3.36641 14.7453 3.39648 15.085 3.27305L16.126 2.89492C16.8671 2.62578 17.6917 2.9125 18.0875 3.57695L19.7825 6.42266C20.1783 7.08672 20.0219 7.92266 19.4109 8.41016L18.5525 9.09492C18.2724 9.31836 18.112 9.64805 18.112 9.99961C18.112 10.3512 18.2728 10.6809 18.5525 10.9043L19.4109 11.5891C20.0219 12.0766 20.1779 12.9125 19.7825 13.5766L18.0875 16.4223C17.6917 17.0867 16.8667 17.3734 16.1256 17.1043L15.0846 16.7262C14.7449 16.6027 14.3701 16.6328 14.0557 16.8086C13.7418 16.9844 13.5274 17.284 13.4677 17.6309L13.2852 18.6938C13.155 19.4504 12.4864 19.9992 11.6949 19.9992V20ZM5.34097 15.0887C5.82942 15.0887 6.31465 15.2125 6.7499 15.4559C7.48298 15.866 7.98272 16.5652 8.12216 17.3742L8.30513 18.4371H11.6953L11.8778 17.3742C12.0169 16.5652 12.517 15.8656 13.2501 15.4559C13.9832 15.0457 14.8577 14.9754 15.6504 15.2633L16.6914 15.6414L18.3865 12.7961L17.5281 12.1109C16.8748 11.5895 16.5 10.8203 16.5 10C16.5 9.17969 16.8748 8.41016 17.5281 7.88906L18.3865 7.2043H18.3869L16.6914 4.35938L15.6504 4.7375C14.8581 5.02539 13.9832 4.95508 13.2501 4.54492C12.517 4.13477 12.0169 3.43555 11.8778 2.62656L11.6953 1.56367H8.30513L8.12216 2.62656C7.98312 3.43555 7.48298 4.13477 6.7499 4.54492C6.01682 4.95508 5.14188 5.02539 4.34956 4.7375L3.30858 4.35938L1.61351 7.20508L2.47193 7.88984C3.12521 8.41133 3.50001 9.18047 3.50001 10.0008C3.50001 10.8211 3.12521 11.5906 2.47193 12.1117L1.61351 12.7969L3.30858 15.6422L4.34956 15.2641C4.67157 15.1473 5.00688 15.0895 5.34097 15.0895V15.0887ZM10.0002 13.8543C7.80741 13.8543 6.02368 12.1254 6.02368 10C6.02368 7.87461 7.80741 6.1457 10.0002 6.1457C12.193 6.1457 13.9767 7.87461 13.9767 10C13.9767 12.1254 12.193 13.8543 10.0002 13.8543ZM10.0002 7.7082C8.69646 7.7082 7.63573 8.73633 7.63573 10C7.63573 11.2637 8.69646 12.2918 10.0002 12.2918C11.3039 12.2918 12.3647 11.2637 12.3647 10C12.3647 8.73633 11.3039 7.7082 10.0002 7.7082Z" />
            </svg>
    );
};

export default Setting;
