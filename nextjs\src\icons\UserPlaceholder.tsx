import React from 'react';

const UserPlaceholder = ({ width = 496, height = 496, className }: any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 496 496"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M248 496C384.967 496 496 384.967 496 248C496 111.033 384.967 0 248 0C111.033 0 0 111.033 0 248C0 384.967 111.033 496 248 496Z"
                fill="#E6E9FF"
            />
            <path
                d="M447.018 395.8C423.889 364.834 393.856 339.69 359.306 322.366C324.755 305.041 286.641 296.014 247.991 296.002C209.34 295.99 171.22 304.992 136.658 322.294C102.097 339.596 72.0479 364.721 48.8984 395.672C71.9295 426.78 101.929 452.059 136.492 469.482C171.055 486.905 209.22 495.987 247.926 495.999C286.632 496.011 324.803 486.954 359.377 469.554C393.951 452.153 423.967 426.893 447.018 395.8Z"
                fill="#5065F6"
            />
            <path
                d="M248 264C301.019 264 344 221.019 344 168C344 114.981 301.019 72 248 72C194.981 72 152 114.981 152 168C152 221.019 194.981 264 248 264Z"
                fill="#5065F6"
            />
        </svg>
    );
};

export default UserPlaceholder;
