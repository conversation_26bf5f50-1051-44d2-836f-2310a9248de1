import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from Service.config import Local, LocalPayload, BaseConfig
from Service.api_service import APIService
from locust import HttpUser, task, between

class LoadTestUser(HttpUser):
    wait_time = between(1, 5)

    host = Local.HOST
    # Define the base URL and other configuration for APIService
    def on_start(self):
        # Create the API service instance using the client, host, origin, and JWT token
        self.api_service = APIService(
            client=self.client,
            host=Local.HOST,
            origin=Local.ORIGIN,
            # jwt_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************.wUUhGCdEWmrXeC0BoMq5TsqqKCrKAfFfQMPNuQNlmWE")
            jwt_token=BaseConfig.JWT_TOKEN)

    # @task
    # def hello_world(self):
    #     self.api_service.get_ping()

    @task
    def post_api_test(self):
        url = Local.VECTOR_STORE_URL
        payload = LocalPayload.VECTOR_STORE
        self.api_service.post_title(url, payload)

    def on_stop(self):
        # Generate the PDF report when the test stops
        self.api_service.generate_pdf_report()
