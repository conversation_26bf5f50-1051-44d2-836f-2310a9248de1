import React from 'react';

export const SettingsIcon = ({height, width, className}:any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M11.6949 20H8.30473C7.51361 20 6.84461 19.4508 6.71444 18.6945L6.53147 17.6316C6.47183 17.2848 6.25742 16.9852 5.94348 16.8094C5.62953 16.6336 5.25433 16.6035 4.91459 16.727L3.8736 17.1051C3.13287 17.3742 2.3075 17.0875 1.91214 16.423L0.217474 13.5773C-0.178285 12.9133 -0.0219158 12.0773 0.589051 11.5898L1.44747 10.9047C1.72756 10.6813 1.88796 10.3516 1.88796 10C1.88796 9.64844 1.72756 9.31875 1.44747 9.09531L0.589051 8.41055C-0.0219158 7.92305 -0.177882 7.08711 0.217474 6.42305L1.91254 3.57734C2.3083 2.91328 3.13327 2.62617 3.87401 2.89531L4.91499 3.27344C5.25473 3.39688 5.62953 3.3668 5.94388 3.19102C6.25783 3.01523 6.47223 2.71563 6.53188 2.36875L6.71484 1.30586C6.84502 0.549219 7.51361 0 8.30513 0H11.6953C12.4864 0 13.1554 0.549219 13.2852 1.30547L13.4681 2.36836C13.5278 2.71523 13.7422 3.01484 14.0561 3.19062C14.3701 3.36641 14.7453 3.39648 15.085 3.27305L16.126 2.89492C16.8671 2.62578 17.6917 2.9125 18.0875 3.57695L19.7825 6.42266C20.1783 7.08672 20.0219 7.92266 19.4109 8.41016L18.5525 9.09492C18.2724 9.31836 18.112 9.64805 18.112 9.99961C18.112 10.3512 18.2728 10.6809 18.5525 10.9043L19.4109 11.5891C20.0219 12.0766 20.1779 12.9125 19.7825 13.5766L18.0875 16.4223C17.6917 17.0867 16.8667 17.3734 16.1256 17.1043L15.0846 16.7262C14.7449 16.6027 14.3701 16.6328 14.0557 16.8086C13.7418 16.9844 13.5274 17.284 13.4677 17.6309L13.2852 18.6938C13.155 19.4504 12.4864 19.9992 11.6949 19.9992V20ZM5.34097 15.0887C5.82942 15.0887 6.31465 15.2125 6.7499 15.4559C7.48298 15.866 7.98272 16.5652 8.12216 17.3742L8.30513 18.4371H11.6953L11.8778 17.3742C12.0169 16.5652 12.517 15.8656 13.2501 15.4559C13.9832 15.0457 14.8577 14.9754 15.6504 15.2633L16.6914 15.6414L18.3865 12.7961L17.5281 12.1109C16.8748 11.5895 16.5 10.8203 16.5 10C16.5 9.17969 16.8748 8.41016 17.5281 7.88906L18.3865 7.2043H18.3869L16.6914 4.35938L15.6504 4.7375C14.8581 5.02539 13.9832 4.95508 13.2501 4.54492C12.517 4.13477 12.0169 3.43555 11.8778 2.62656L11.6953 1.56367H8.30513L8.12216 2.62656C7.98312 3.43555 7.48298 4.13477 6.7499 4.54492C6.01682 4.95508 5.14188 5.02539 4.34956 4.7375L3.30858 4.35938L1.61351 7.20508L2.47193 7.88984C3.12521 8.41133 3.50001 9.18047 3.50001 10.0008C3.50001 10.8211 3.12521 11.5906 2.47193 12.1117L1.61351 12.7969L3.30858 15.6422L4.34956 15.2641C4.67157 15.1473 5.00688 15.0895 5.34097 15.0895V15.0887ZM10.0002 13.8543C7.80741 13.8543 6.02368 12.1254 6.02368 10C6.02368 7.87461 7.80741 6.1457 10.0002 6.1457C12.193 6.1457 13.9767 7.87461 13.9767 10C13.9767 12.1254 12.193 13.8543 10.0002 13.8543ZM10.0002 7.7082C8.69646 7.7082 7.63573 8.73633 7.63573 10C7.63573 11.2637 8.69646 12.2918 10.0002 12.2918C11.3039 12.2918 12.3647 11.2637 12.3647 10C12.3647 8.73633 11.3039 7.7082 10.0002 7.7082Z" />
        </svg>
    );
};

export const DataControlIcon = ({height, width, className}:any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M19.2925 14.3L18.9038 14.1106C18.9238 13.8515 18.9238 13.5825 18.8839 13.3632L19.2925 13.134C19.8705 12.8052 20.1295 12.0977 19.9003 11.4699C19.6612 10.8421 19.3323 10.2641 18.9138 9.74589C18.6547 9.43697 18.2661 9.26756 17.8675 9.26756C17.6183 9.26756 17.3792 9.32735 17.1699 9.4569L16.8012 9.6861C16.6119 9.56652 16.4225 9.44694 16.2431 9.38715C16.2431 9.28749 16.2431 9.18784 16.2431 9.08819V3.51769C16.2431 1.20578 12.1375 0 8.09168 0C4.04584 0 0 1.20578 0 3.51769V14.6388C0 16.9507 4.10563 18.1565 8.15147 18.1565C8.69955 18.1565 9.26756 18.1565 9.76582 18.0767C9.9153 18.1266 10.0648 18.1565 10.2242 18.1565C10.4634 18.1565 10.6926 18.0967 10.9018 17.987L11.2606 17.7578C11.4798 17.9073 11.709 18.0369 11.9183 18.1166V18.575C11.9183 19.2626 12.4365 19.8505 13.1141 19.9502C13.423 19.99 13.7319 20.01 14.0409 20.0199C14.3498 20.0199 14.6587 19.99 14.9676 19.9502C15.6452 19.8505 16.1634 19.2626 16.1634 18.575L16.1435 18.1465C16.3827 18.0369 16.6119 17.8974 16.7813 17.7578L17.1898 17.987C17.3991 18.1066 17.6383 18.1764 17.8774 18.1764C18.286 18.1764 18.6746 17.997 18.9337 17.6781C19.3523 17.1599 19.6911 16.582 19.9203 15.9641C20.1495 15.3264 19.8804 14.6288 19.2925 14.3ZM14.6886 18.5252C14.4694 18.5451 14.2501 18.5451 14.0209 18.5451C13.9312 18.5451 13.8416 18.5451 13.7519 18.5451C13.6223 18.5451 13.4928 18.5451 13.3732 18.565V17.6383C13.3732 17.3293 13.1739 17.0503 12.8849 16.9407C12.4863 16.8012 12.1276 16.5919 11.8087 16.3229C11.6791 16.2033 11.5097 16.1435 11.3303 16.1435C11.2008 16.1435 11.0713 16.1834 10.9517 16.2431L10.284 16.7414C9.98505 16.3727 9.72596 15.9641 9.50673 15.5954L10.3039 15.137C10.573 14.9875 10.7225 14.6786 10.6627 14.3697C10.6228 14.1505 10.5929 13.9312 10.6029 13.712C10.6029 13.5027 10.6228 13.2935 10.6627 13.0842C10.7125 12.7753 10.573 12.4664 10.294 12.3069L9.52666 11.9781C9.69606 11.5197 9.92526 11.0912 10.1545 10.7125L10.9616 11.1709C11.0713 11.2307 11.2008 11.2706 11.3303 11.2706C11.4998 11.2706 11.6692 11.2108 11.8087 11.1011C12.1276 10.8221 12.4863 10.6129 12.8849 10.4833C13.1739 10.3737 13.3732 10.0947 13.3732 9.78575L13.3433 8.94868C13.5825 8.90882 13.8216 8.89886 14.0608 8.89886L14.7185 8.85899V9.78575C14.7185 10.0947 14.9178 10.3737 15.2068 10.4833C15.6054 10.6129 15.9641 10.8221 16.283 11.1111C16.4225 11.2207 16.5919 11.2805 16.7713 11.2805C16.9008 11.2805 17.0204 11.2506 17.14 11.1908L17.8077 10.6826C18.0967 11.0613 18.3558 11.4699 18.565 11.8485L17.7578 12.3069C17.4589 12.4464 17.2995 12.7753 17.3692 13.1041C17.4091 13.3134 17.419 13.5227 17.419 13.7319C17.419 13.9412 17.3991 14.1505 17.3692 14.3498C17.3094 14.6587 17.4489 14.9676 17.728 15.1271L18.5052 15.4559C18.3259 15.9043 18.0967 16.3328 17.8774 16.7215L17.0703 16.2631C16.9606 16.2033 16.8311 16.1634 16.7015 16.1634C16.5222 16.1634 16.3528 16.2232 16.2232 16.3428C15.9043 16.6119 15.5356 16.8211 15.137 16.9606C14.848 17.0703 14.6487 17.3493 14.6487 17.6582L14.6886 18.5451V18.5252ZM14.868 7.48381C14.579 7.44395 14.28 7.42402 13.991 7.42402C13.702 7.42402 13.4031 7.44395 13.1041 7.48381C12.4165 7.58346 11.9183 8.16144 11.9083 8.85899L11.9283 9.28749C11.6891 9.39711 11.4599 9.52666 11.2905 9.67613L10.8919 9.44694C10.6826 9.32735 10.4434 9.2576 10.1943 9.2576C9.78575 9.2576 9.40708 9.43697 9.13802 9.74589C8.7992 10.1744 8.51021 10.6428 8.33084 11.1211H8.13154C4.0558 11.1211 1.45491 9.90533 1.45491 9.07823V5.6004C3.34828 6.54709 5.46089 7.05531 7.57349 7.05531C7.76283 7.05531 7.9422 7.05531 8.13154 7.04534C8.31091 7.04534 8.49028 7.05531 8.66966 7.05531C10.6029 7.05531 12.5162 6.61684 14.2402 5.7997C14.4295 5.71998 14.6288 5.65022 14.8082 5.55057L14.848 7.24464V7.48381H14.868ZM14.8281 3.51769C14.8281 4.33483 12.1674 5.56054 8.15147 5.56054C4.13553 5.56054 1.47484 4.33483 1.47484 3.51769C1.47484 2.70055 4.13553 1.47484 8.15147 1.47484C12.1674 1.47484 14.8281 2.71051 14.8281 3.51769ZM1.47484 14.6388V11.1609C3.36821 12.1076 5.49078 12.6059 7.61335 12.6059C7.79273 12.6059 7.98206 12.6059 8.16144 12.5959L8.25112 12.576C8.3707 12.8052 8.55008 12.9945 8.77927 13.134L9.15795 13.3333L9.17788 14.0807L8.77927 14.3C8.18137 14.6188 7.90234 15.3164 8.12157 15.9641C8.21126 16.1933 8.32088 16.4126 8.46039 16.5919C8.48032 16.6218 8.49028 16.6517 8.50025 16.6816H8.03189C4.03587 16.6816 1.47484 15.4758 1.47484 14.6487V14.6388Z" />
        </svg>
    );
};

export const MembersIcon = ({height, width, className}:any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M5.16667 4.33333C5.16667 2.87667 6.37667 1.66667 7.83333 1.66667C8.9875 1.66667 9.9875 2.4275 10.3525 3.4675C10.8853 3.28471 11.4428 3.18384 12.0058 3.16833C11.4917 1.35 9.8075 0 7.83333 0C5.45667 0 3.5 1.95667 3.5 4.33333C3.50106 4.92821 3.62483 5.51647 3.86357 6.06134C4.10231 6.60621 4.45088 7.09597 4.8875 7.5C2.03333 8.675 0 11.495 0 14.75C0 14.971 0.0877974 15.183 0.244078 15.3393C0.400358 15.4955 0.61232 15.5833 0.833333 15.5833C1.05435 15.5833 1.26631 15.4955 1.42259 15.3393C1.57887 15.183 1.66667 14.971 1.66667 14.75C1.66667 11.8042 3.7975 9.3 6.58333 8.71333C6.5875 8.05417 6.70583 7.42167 6.92 6.835C5.90333 6.45667 5.16667 5.47 5.16667 4.33333Z" />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.0585 11.8942C15.7119 11.3087 16.1722 10.5388 16.3786 9.68611C16.5849 8.83343 16.5276 7.9382 16.2142 7.1188C15.9009 6.29939 15.3461 5.5944 14.6234 5.09706C13.9008 4.59971 13.0441 4.33344 12.1668 4.33344C11.2895 4.33344 10.4329 4.59971 9.71022 5.09706C8.98752 5.5944 8.4328 6.29939 8.11941 7.1188C7.80603 7.9382 7.74873 8.83343 7.95509 9.68611C8.16146 10.5388 8.62177 11.3087 9.27516 11.8942C6.39183 13.0558 4.3335 15.8917 4.3335 19.1667C4.3335 19.3877 4.42129 19.5996 4.57757 19.7559C4.73385 19.9122 4.94582 20 5.16683 20C5.38784 20 5.5998 19.9122 5.75609 19.7559C5.91237 19.5996 6.00016 19.3877 6.00016 19.1667C6.00016 15.8 8.78266 13.0117 12.146 13H12.1877C15.5502 13.0117 18.3335 15.8 18.3335 19.1667C18.3335 19.3877 18.4213 19.5996 18.5776 19.7559C18.7339 19.9122 18.9458 20 19.1668 20C19.3878 20 19.5998 19.9122 19.7561 19.7559C19.9124 19.5996 20.0002 19.3877 20.0002 19.1667C20.0002 15.8917 17.941 13.0558 15.0585 11.8942ZM12.1668 5.99999C11.4596 5.99756 10.7803 6.27618 10.2785 6.77456C9.77671 7.27294 9.49343 7.95025 9.491 8.65749C9.48856 9.36473 9.76719 10.044 10.2656 10.5458C10.7639 11.0476 11.4413 11.3309 12.1485 11.3333H12.1852C12.8924 11.3309 13.5697 11.0476 14.0681 10.5458C14.5665 10.044 14.8451 9.36473 14.8427 8.65749C14.8402 7.95025 14.5569 7.27294 14.0551 6.77456C13.5533 6.27618 12.8741 5.99756 12.1668 5.99999Z"
            />
        </svg>
    );
};

export const BillingIcon = ({height, width, className}:any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 22"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M10.8966 5.44513H5.0827C4.88996 5.44513 4.70511 5.5217 4.56881 5.65799C4.43252 5.79428 4.35596 5.97913 4.35596 6.17187C4.35596 6.36461 4.43252 6.54946 4.56881 6.68575C4.70511 6.82204 4.88996 6.89861 5.0827 6.89861H10.8966C11.0894 6.89861 11.2742 6.82204 11.4105 6.68575C11.5468 6.54946 11.6234 6.36461 11.6234 6.17187C11.6234 5.97913 11.5468 5.79428 11.4105 5.65799C11.2742 5.5217 11.0894 5.44513 10.8966 5.44513ZM10.8966 9.3332H5.0827C4.88996 9.3332 4.70511 9.40976 4.56881 9.54605C4.43252 9.68234 4.35596 9.86719 4.35596 10.0599C4.35596 10.2527 4.43252 10.4375 4.56881 10.5738C4.70511 10.7101 4.88996 10.7867 5.0827 10.7867H10.8966C11.0894 10.7867 11.2742 10.7101 11.4105 10.5738C11.5468 10.4375 11.6234 10.2527 11.6234 10.0599C11.6234 9.86719 11.5468 9.68234 11.4105 9.54605C11.2742 9.40976 11.0894 9.3332 10.8966 9.3332ZM10.8966 13.3085H5.0827C4.88996 13.3085 4.70511 13.385 4.56881 13.5213C4.43252 13.6576 4.35596 13.8425 4.35596 14.0352C4.35596 14.228 4.43252 14.4128 4.56881 14.5491C4.70511 14.6854 4.88996 14.762 5.0827 14.762H10.8966C11.0894 14.762 11.2742 14.6854 11.4105 14.5491C11.5468 14.4128 11.6234 14.228 11.6234 14.0352C11.6234 13.8425 11.5468 13.6576 11.4105 13.5213C11.2742 13.385 11.0894 13.3085 10.8966 13.3085Z" />
            <path d="M17.1729 0H2.82706C2.45424 0.00095104 2.08527 0.0754462 1.74129 0.219218C1.39731 0.36299 1.08507 0.573215 0.822461 0.837849C0.559852 1.10248 0.352033 1.41633 0.210908 1.7614C0.0697826 2.10648 -0.00187579 2.47601 3.73196e-05 2.84883V20.5741C-0.000438203 20.7559 0.0475098 20.9347 0.138957 21.0919C0.230404 21.2491 0.362055 21.3792 0.520388 21.4687C0.678722 21.5582 0.858031 21.604 1.0399 21.6013C1.22177 21.5986 1.39965 21.5476 1.55526 21.4534L4.22967 19.8473C4.27002 19.8222 4.31659 19.8089 4.36412 19.8089C4.41165 19.8089 4.45822 19.8222 4.49857 19.8473L7.47094 21.6278C7.62728 21.7275 7.8088 21.7804 7.99419 21.7804C8.18074 21.7795 8.36373 21.7293 8.52471 21.6351L11.5116 19.8473C11.552 19.8222 11.5985 19.8089 11.6461 19.8089C11.6936 19.8089 11.7402 19.8222 11.7805 19.8473L14.4404 21.4534C14.5965 21.5462 14.7748 21.5951 14.9564 21.5951C15.138 21.5951 15.3162 21.5462 15.4724 21.4534C15.6287 21.364 15.7587 21.235 15.8493 21.0793C15.9399 20.9237 15.9878 20.7469 15.9883 20.5668V8.31392H18.2703C18.7285 8.31201 19.1673 8.12917 19.4912 7.80521C19.8152 7.48125 19.998 7.04242 20 6.58428V2.84883C20.0019 2.47601 19.9302 2.10648 19.7891 1.7614C19.648 1.41633 19.4401 1.10248 19.1775 0.837849C18.9149 0.573215 18.6027 0.36299 18.2587 0.219218C17.9147 0.0754462 17.5458 0.00095104 17.1729 0ZM14.5349 19.811L12.5291 18.6046C12.2618 18.4459 11.9568 18.3622 11.6461 18.3622C11.3353 18.3622 11.0303 18.4459 10.7631 18.6046L8.00146 20.2616L5.24711 18.5973C4.97672 18.4356 4.66728 18.3508 4.35221 18.3521C4.03714 18.3533 3.7284 18.4407 3.45933 18.6046L1.45352 19.8182V2.84883C1.45063 2.66662 1.48402 2.48567 1.55175 2.3165C1.61948 2.14733 1.7202 1.99332 1.84803 1.86346C1.97587 1.7336 2.12826 1.63047 2.29634 1.56008C2.46443 1.4897 2.64484 1.45346 2.82706 1.45348H14.8328C14.6409 1.81911 14.5388 2.22516 14.5349 2.63807V19.7892V19.811ZM18.5465 6.58428C18.5465 6.65752 18.5174 6.72776 18.4656 6.77955C18.4138 6.83134 18.3436 6.86044 18.2703 6.86044H15.9883V2.65987C15.9855 2.50249 16.014 2.3461 16.0722 2.19985C16.1304 2.05361 16.2172 1.92043 16.3275 1.80811C16.4378 1.69579 16.5694 1.60658 16.7145 1.54569C16.8597 1.4848 17.0155 1.45346 17.1729 1.45348C17.5372 1.45348 17.8866 1.59819 18.1442 1.85578C18.4018 2.11337 18.5465 2.46274 18.5465 2.82702V6.58428Z" />
        </svg>
    );
};
