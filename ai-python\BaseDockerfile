# ========= STAGE 1: Builder ==========
FROM python:3.10-slim AS builder

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    CRAWL4AI_CACHE=/crawl4ai-cache

# Install build + crawl4ai dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    build-essential \
    python3-dev \
    libev-dev \
    libpoppler-cpp-dev \
    pkg-config \
    antiword \
    unrtf \
    poppler-utils \
    tesseract-ocr \
    flac \
    ffmpeg \
    lame \
    libmad0 \
    libsox-fmt-mp3 \
    sox \
    libjpeg-dev \
    swig \
    libpulse-dev \
    curl \
 && rm -rf /var/lib/apt/lists/*

# Install Python packages
RUN pip install --no-cache-dir \
    crawl4ai \
    lxml-html-clean

# Install app dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Setup crawl4ai cache if needed
RUN mkdir -p /crawl4ai-cache && chmod -R 777 /crawl4ai-cache


# ========= STAGE 2: Runtime Image ==========
FROM python:3.10-slim AS final

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    CRAWL4AI_CACHE=/crawl4ai-cache

# Install runtime system packages (media + OCR support)
RUN apt-get update && apt-get install -y --no-install-recommends \
    libev-dev \
    libpoppler-cpp-dev \
    antiword \
    unrtf \
    poppler-utils \
    tesseract-ocr \
    flac \
    ffmpeg \
    lame \
    libmad0 \
    libsox-fmt-mp3 \
    sox \
    libjpeg-dev \
    libpulse-dev \
 && rm -rf /var/lib/apt/lists/*

# Copy Python + installed packages
COPY --from=builder /usr/local /usr/local

# Copy Crawl4AI cache if needed
COPY --from=builder /crawl4ai-cache /crawl4ai-cache