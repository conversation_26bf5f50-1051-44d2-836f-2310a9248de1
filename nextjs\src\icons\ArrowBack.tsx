import React from 'react';

const ArrowBack = ({height, width, className}:any) => {
    return (
        <svg className={className} width={width} height={height}
            viewBox="0 0 17 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M15.7498 5.25922H2.56034L6.53009 1.28947C6.60172 1.22028 6.65886 1.13752 6.69817 1.04602C6.73747 0.954516 6.75816 0.856102 6.75903 0.756517C6.75989 0.656933 6.74092 0.558173 6.70321 0.466001C6.6655 0.373828 6.60981 0.290089 6.53939 0.21967C6.46897 0.14925 6.38523 0.0935604 6.29306 0.0558497C6.20088 0.0181391 6.10212 -0.000837044 6.00254 2.83178e-05C5.90295 0.00089368 5.80454 0.0215834 5.71304 0.0608901C5.62153 0.100197 5.53877 0.157334 5.46959 0.228966L0.21959 5.47897C0.0789866 5.61961 0 5.81034 0 6.00922C0 6.20809 0.0789866 6.39882 0.21959 6.53947L5.46959 11.7895C5.61104 11.9261 5.80049 12.0017 5.99714 12C6.19379 11.9983 6.3819 11.9194 6.52095 11.7803C6.66001 11.6413 6.73889 11.4532 6.74059 11.2565C6.7423 11.0599 6.66671 10.8704 6.53009 10.729L2.56034 6.75922H15.7498C15.9488 6.75922 16.1395 6.6802 16.2802 6.53955C16.4208 6.39889 16.4998 6.20813 16.4998 6.00922C16.4998 5.8103 16.4208 5.61954 16.2802 5.47889C16.1395 5.33823 15.9488 5.25922 15.7498 5.25922Z" />
        </svg>
    );
};

export default ArrowBack;
