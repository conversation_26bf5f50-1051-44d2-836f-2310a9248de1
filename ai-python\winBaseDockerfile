# ========= STAGE 1: Builder ==========
FROM python:3.10-slim AS builder

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    CRAWL4AI_CACHE=/crawl4ai-cache

# Install build + Playwright dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    build-essential \
    python3-dev \
    libev-dev \
    libpoppler-cpp-dev \
    pkg-config \
    antiword \
    unrtf \
    poppler-utils \
    tesseract-ocr \
    flac \
    ffmpeg \
    lame \
    libmad0 \
    libsox-fmt-mp3 \
    sox \
    libjpeg-dev \
    swig \
    libpulse-dev \
    curl \
    # Add required system libraries for Chromium (GTK + ATK + X11)
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm-dev \
    libnss3 \
    libxshmfence-dev \
    libxss1 \
    libxtst6 \
    libpci3 \
    libgdk-pixbuf2.0-0 \
    libgtk-3-0 \
 && rm -rf /var/lib/apt/lists/*

RUN pip install --no-cache-dir \
    torch==2.2.2+cpu -f https://download.pytorch.org/whl/cpu/torch_stable.html \
    crawl4ai \
    lxml-html-clean \
    playwright==1.51.0 
# Install browser binaries (Chromium)
RUN playwright install chromium --with-deps

# Install app dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# OPTIONAL: If crawl4ai or playwright generates cache/configs in home
RUN mkdir -p /ms-playwright /crawl4ai-cache && chmod -R 777 /ms-playwright /crawl4ai-cache


# ========= STAGE 2: Runtime Image ==========
FROM python:3.10-slim AS final

ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    CRAWL4AI_CACHE=/crawl4ai-cache

# Install runtime system packages (Playwright + media + OCR support)
RUN apt-get update && apt-get install -y --no-install-recommends \
    libev-dev \
    libpoppler-cpp-dev \
    antiword \
    unrtf \
    poppler-utils \
    tesseract-ocr \
    flac \
    ffmpeg \
    lame \
    libmad0 \
    libsox-fmt-mp3 \
    sox \
    libjpeg-dev \
    libpulse-dev \
    # Playwright's Chromium dependencies (GTK + ATK + X11)
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm-dev \
    libnss3 \
    libxshmfence-dev \
    libxss1 \
    libxtst6 \
    libpci3 \
    libgdk-pixbuf2.0-0 \
    libgtk-3-0 \
 && rm -rf /var/lib/apt/lists/*

# Copy Python + installed packages
COPY --from=builder /usr/local /usr/local

# Copy Playwright browser binaries
COPY --from=builder /ms-playwright /ms-playwright

# Copy Crawl4AI cache if needed
COPY --from=builder /crawl4ai-cache /crawl4ai-cache