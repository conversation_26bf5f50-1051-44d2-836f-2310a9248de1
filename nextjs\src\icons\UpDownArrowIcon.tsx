import React from 'react';

const UpDownArrowIcon = ({width, height, className}:any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 15 15"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M4.93259 5.4316C4.75685 5.60734 4.75685 5.89226 4.93259 6.068C5.10833 6.24373 5.39325 6.24373 5.56899 6.068L7.50079 4.13619L9.43259 6.068C9.60833 6.24373 9.89325 6.24373 10.069 6.068C10.2447 5.89226 10.2447 5.60734 10.069 5.4316L7.81899 3.1816C7.73459 3.09721 7.62013 3.0498 7.50079 3.0498C7.38144 3.0498 7.26698 3.09721 7.18259 3.1816L4.93259 5.4316ZM10.069 9.568C10.2447 9.39226 10.2447 9.10735 10.069 8.93161C9.89325 8.75588 9.60833 8.75588 9.43259 8.93161L7.50079 10.8634L5.56899 8.93161C5.39325 8.75588 5.10833 8.75588 4.93259 8.93161C4.75685 9.10735 4.75685 9.39226 4.93259 9.568L7.18259 11.818C7.35833 11.9937 7.64325 11.9937 7.81899 11.818L10.069 9.568Z"
               
            />
        </svg>
    );
};

export default UpDownArrowIcon;
