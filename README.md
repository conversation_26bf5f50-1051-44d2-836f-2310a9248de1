<p align="center">
  <img src="nodejs/public/images/weam-banner.png" alt="Weam AI Logo" width="600" height="600"/>
</p>

<p align="center">
  <strong>LLMs, Agents, AI Apps, and Team—In One Place, Ready to Use and Expand</strong><br/>
</p>

<p align="center">
  <a href="https://weam.ai">Website</a> •
  <a href="https://docs.weam.ai">Docs</a> •
  <a href="https://discord.com/invite/EyUbyUxf4n">Discord</a>
</p>

<p align="center">
  <a href="https://github.com/weam-ai/weamai/stargazers"><img src="https://img.shields.io/github/stars/weam-ai/weamai?style=flat-square&color=yellow" /></a>
  <a href="https://github.com/weam-ai/weamai/forks"><img src="https://img.shields.io/github/forks/weam-ai/weamai?style=flat-square&color=blue" /></a>
  <a href="https://github.com/weam-ai/weamai/issues"><img src="https://img.shields.io/github/issues/weam-ai/weamai?style=flat-square" /></a>
<!--   <a href="https://hub.docker.com/r/weamai/weam"><img src="https://img.shields.io/docker/pulls/weamai/weamai?style=flat-square&color=informational" /></a> -->
  <a href="https://github.com/weam-ai/weamai"><img src="https://img.shields.io/github/commit-activity/m/weam-ai/weamai?style=flat-square&color=green" /></a>
</p>

---

## What is Weam AI

Weam is an open source platform that helps teams adopt AI systematically. It includes a complete, production-ready stack with Next.js frontend and Node.js/Python backend - ready to deploy and use immediately.

**System Requirements:** CPU with 4+ cores, 8GB+ RAM. Professional installation support available for non-technical teams.  

**Getting Started:** Simply download or fork the repository and self-host it on your infrastructure.

**Why Weam?**  
Modern teams need easy access to the latest AI models. Weam connects to all major LLM providers out of the box - just add your API keys and you're ready to go. Think of it as ChatGPT built specifically for teams.  
All your organization's AI interactions live in one centralized platform, organized into "Brains" (intelligent folders). These Brains contain not just chat histories, but also your custom prompts and AI agents. You can structure Brains around your departments - Marketing, Sales, Engineering, Support - whatever fits your organization.

But we didn't stop at chat. Weam is built to grow with your needs. Customize existing features or add entirely new AI applications. With the rise of AI-powered development tools like Loveable and Cursor, your team can rapidly build custom AI apps. Instead of scattered deployments, bring these apps into Weam where your entire team already works. We're building a library of open source AI apps you can use as-is, customize, or use as inspiration for your own.

**Bottom line:** You get a production-grade AI platform that's ready to deploy today and can expand as your needs grow. Completely free and open source. 

## 🎥 Watch the Demo

[![Watch the video](https://img.youtube.com/vi/L-CS9zVchqs/maxresdefault.jpg)](https://www.youtube.com/watch?v=L-CS9zVchqs)

---

### 📚 Documentation Hub


Visit our comprehensive [**Documentation**](https://docs.weam.ai/).
 for:
- Step-by-step installation guides  
- Video tutorials for all features  
- Technical architecture details  
- API references and more  

---

### Platform Features

#### 💬 Chat System
- **Multi-LLM Support:** OpenAI, Anthropic, Gemini, Llama, Perplexity, DeepSeek, Open Router, Hugging Face, Grok, and more  
- **Intelligent Context:** Full conversation history maintains coherent, context-aware discussions  
- **Local Model Support:** Connect to self-hosted models (coming soon)  
- **Conversation Management:** Fork chats to explore different directions  
- **Voice Input:** Speak instead of type with built-in voice-to-text  

#### 🔧 Productivity Tools
- **Web Scraping:** Extract content from any URL to provide context to your AI  
- **Web Search:** Real-time information retrieval during conversations  
- **Team Collaboration:** Multiple team members can work together in the same chat  
- **Threaded Comments:** Slack-style commenting for organized discussions  

#### 🔗 Sharing & Access
- **Team Management:** Add or remove team members from specific chats  
- **Public Sharing:** Generate shareable links for external collaboration  

#### 📝 Prompt Library
- **Custom Prompts:** Create and save prompts for repeated use  
- **Team Library:** Build a shared repository of proven prompts  
- **Template Collection:** Access pre-built prompts for common tasks  
- **Smart Generation:** Auto-create prompts by scraping websites  
- **Organization:** Tag and favorite prompts for quick access  
- **Quick Access:** Type "/" in any chat to instantly access your prompts  
- **Prompt Enhancement:** AI-powered feature that transforms basic queries into comprehensive prompts  
- **Real Example:** Input a website URL and basic information → receive a complete prompt with website summary and context  

#### 🤖 AI Agents
- **Custom Instructions:** Define specific behaviors and knowledge for each agent  
- **Knowledge Base:** Upload documents to create specialized agents  
- **Model Selection:** Choose the optimal AI model for each agent's purpose  
- **MCP Integration:** Model Context Protocol support coming soon  
- **Quick Deployment:** Access any agent instantly by typing "@" in chat  
- **Persistent Sessions:** Selected agents remain active throughout conversations  

#### 🧠 RAG (Retrieval-Augmented Generation)
- **Complete Pipeline:** Built-in document processing system  
- **Smart Processing:** Documents are automatically chunked, embedded, and vectorized  
- **Intelligent Retrieval:** User queries trigger semantic search across document chunks  
- **Context Injection:** Relevant information is seamlessly provided to the LLM  
- **Agent Integration:** RAG pipeline automatically processes documents uploaded to agents  

#### 🔌 MCP (Model Context Protocol)
- **Growing Integrations:** Gmail, Slack, Google Drive, and more being added regularly  
- **Developer Friendly:** Follow our documentation to add your own MCP connections  

#### 🏢 Enterprise Features
- **Multi-Workspace:** Create separate environments for different teams or projects  
- **Brain Organization:** Shared folders for teams with private areas for individual work  
- **Access Control:** Granular permissions for chats, prompts, and agents  
- **Usage Analytics:** Admin dashboard showing team member activity  
- **Team Groups:** Organize users for efficient permission management  

#### 🚀 Pro Agents (Pre-built Automations)
Ready-to-use automation workflows powered by specialized APIs:
- **QA Agent:** Comprehensive 70-point website analysis including Google PageSpeed Insights  
- **Video Analyzer:** Process Loom recordings by analyzing both video frames and audio to generate actionable task lists  
- **Sales Call Analyzer:** Transform Fathom recordings into detailed call insights with customizable analysis prompts  
- **Proposal Generator:** Input project basics and receive professionally formatted, editable multi-page proposals  
- **SEO Content Writer:** Advanced blog creation pipeline powered by DataforSEO:  
  - Start with any URL for context  
  - Auto-generates business summary  
  - Suggests relevant keywords based on competition analysis  
  - Researches top-ranking content for each topic  
  - Checks sitemap to avoid duplicate content  
  - Produces comprehensive, Google-optimized articles using sophisticated 2-page prompts  
- **Build your own**  

#### 🎨 AI App Solutions  
**Coming Soon:**
- **AI Presentation Maker:** Create stunning presentations like Gamma  
- **AI Document Editor:** Intelligent document creation and editing  
- **Voice Agent:** AI that makes calls on your behalf  
- **Build Your Own:** Integrate custom AI apps built with modern tools directly into Weam, creating a unified AI workspace for your entire organization.

---

## Installation Options

```bash
git clone https://github.com/weam-ai/weam.git
cd weam
cp .env.example .env
bash build.sh (for mac/linux)
or 
bash winbuild.sh  (for windows)
docker-compose up -d
```

- Docs: [Quickstart Guide](https://docs.weam.ai/quickstart)
- From Source / Cloud: Coming soon
- Setup Video: Uploading soon

---

## Getting Started

1. Install with Docker (2 minutes)  
2. Add your LLM API keys (OpenAI, Claude, etc.)  
3. Create your first Workspace  
4. Add a Brain with files or data  
5. Start chatting with AI  
6. Invite your team and scale collaboration

---

## Community & Support

- [Discord](https://discord.gg/qhuPkhWh)  
- [Docs](https://docs.weam.ai)  
- [GitHub Issues](https://github.com/weam-ai/weam/issues)  
- Email: <EMAIL>  

---

## Contributing

We welcome contributions of all kinds:

- Star this repo to show your support  
- Report issues you encounter  
- Suggest features on our roadmap  
- Submit pull requests for improvements  
- Improve documentation  
-  Design UI/UX improvements  
- Contributor Guide: `CONTRIBUTING.md`

---

## Security & Privacy

- Self-hosted: Your data never leaves your infrastructure  
- Role-based access: Granular permissions and workspace isolation  
- Audit logs: Complete activity tracking  
- API security: OAuth 2.0 and JWT authentication

---

## License

Weam AI is licensed under a modified Apache License 2.0 with additional terms to:

- Protect fair use and encourage contributions  
- Ensure commercial use guidelines  
- Maintain open source principles  
  See [`LICENSE`](./LICENSE) for full terms.

SaaS-style deployment of our open source project requires a commercial license. Learn more about commercial licensing and pricing [**here**](https://weam.ai/commercial-license/)

**Need help setting up?**  
[Installation Support](https://weam.ai/installation-support/) — Get Weam’s production-ready AI platform running fast with our professional installation services, so your team can focus on transforming workflows with AI.

---

## What's Next?

- **Smart Brain Memory:**  Turn your Brain's chat history into organizational memory that LLMs can optionally access for smarter, context-aware responses.
- **Expanded MCP Ecosystem:** More MCP integrations coming soon, plus the ability to connect any MCP service directly to your agents.
- **Advanced Agent Capabilities:** More ready to use Pro agents and AI Apps 


---

<p align="center">
  <strong>Built with ❤️ by the Weam AI community</strong><br/>
  <em>The open source AI platform that brings teams and AI together.</em><br/>
  <a href="https://github.com/weam-ai/weamai">⭐ Star us on GitHub</a> • 
  <a href="https://discord.com/invite/EyUbyUxf4n">Join our Discord</a> • 
  <a href="https://weam.ai">Visit our Website</a>
</p>
