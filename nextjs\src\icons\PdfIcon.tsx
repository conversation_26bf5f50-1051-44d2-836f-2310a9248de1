import React from 'react';

const PdfIcon = ({height, width, className}:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M16 0H0V16H16V0Z" fill="#FF5D3B" />
            <path
                d="M2 5H3.812C4.35067 5 4.764 5.15867 5.05067 5.47733C5.34 5.79333 5.484 6.244 5.484 6.82933C5.484 7.41467 5.34 7.87067 5.05067 8.188C4.764 8.504 4.35067 8.66133 3.812 8.66133H3.092V10.6067H2V5.00133M3.092 6.04667V7.612H3.696C3.908 7.612 4.07067 7.544 4.18667 7.40933C4.30267 7.272 4.36 7.07733 4.36 6.82667C4.36 6.576 4.30267 6.384 4.18667 6.248C4.072 6.11333 3.908 6.04533 3.696 6.04533H3.092M7.344 6.092V9.51333H7.73467C8.18133 9.51333 8.52133 9.36667 8.756 9.07467C8.992 8.78133 9.11067 8.356 9.11067 7.79867C9.11067 7.24133 8.99333 6.82 8.75867 6.52933C8.524 6.23867 8.18267 6.09333 7.73467 6.09333H7.344M6.25333 5H7.404C8.04667 5 8.52533 5.06133 8.83867 5.184C9.15467 5.304 9.42533 5.50933 9.64933 5.8C9.848 6.05333 9.99467 6.344 10.092 6.67467C10.188 7.00533 10.236 7.38 10.236 7.79733C10.236 8.21467 10.188 8.59867 10.092 8.93067C9.996 9.26133 9.848 9.55333 9.64933 9.80533C9.42267 10.096 9.15067 10.3027 8.83333 10.4253C8.516 10.5453 8.04 10.6053 7.404 10.6053H6.25333V5ZM11.0733 5H14.0187V6.092H12.164V7.136H13.908V8.22933H12.164V10.6067H11.072V5"
                fill="#FFF9F9"
            />
        </svg>
    );
};

export default PdfIcon;
