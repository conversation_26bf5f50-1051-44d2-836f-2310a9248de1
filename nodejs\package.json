{"name": "customai-node", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js", "dev:two": "NODE_ENV=two nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@socket.io/redis-adapter": "^8.3.0", "agenda": "^5.0.0", "aws-sdk": "^2.1557.0", "bcrypt": "^5.1.1", "bull": "^4.12.2", "bull-board": "^2.1.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-list-endpoints-descriptor": "^1.0.13", "express-rate-limit": "^7.1.5", "firebase-admin": "^12.2.0", "helmet": "^7.1.0", "i18next": "^23.8.2", "i18next-fs-backend": "^2.3.1", "i18next-http-middleware": "^3.5.0", "joi": "^17.12.1", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "mime-types": "^2.1.35", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mongoose": "^8.1.1", "mongoose-paginate-v2": "^1.8.0", "multer": "1.4.5-lts.1", "multer-s3": "^2.10.0", "newrelic": "latest", "nodemailer": "^6.9.9", "otplib": "^12.0.1", "qrcode": "^1.5.3", "redis": "^4.7.0", "sharp": "^0.33.5", "socket.io": "^4.7.4", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "winston-loki": "^6.1.0"}, "devDependencies": {"jest": "^29.7.0", "morgan": "^1.10.0"}}