import React from 'react';

const UnArchiveIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M0 10.4C0 10.1878 0.0842854 9.98432 0.234315 9.83429C0.384344 9.68426 0.587827 9.59998 0.8 9.59998H3.144C3.53909 9.6 3.92807 9.69757 4.2764 9.88402C4.62474 10.0705 4.92165 10.34 5.1408 10.6688L5.7904 11.644C5.86348 11.7535 5.96246 11.8433 6.07857 11.9054C6.19468 11.9675 6.32433 12 6.456 12H9.544C9.67567 12 9.80532 11.9675 9.92143 11.9054C10.0375 11.8433 10.1365 11.7535 10.2096 11.644L10.8592 10.668C11.0784 10.3394 11.3754 10.07 11.7237 9.88367C12.072 9.69736 12.461 9.59992 12.856 9.59998H15.2C15.4122 9.59998 15.6157 9.68426 15.7657 9.83429C15.9157 9.98432 16 10.1878 16 10.4C16 10.6121 15.9157 10.8156 15.7657 10.9657C15.6157 11.1157 15.4122 11.2 15.2 11.2H12.856C12.7243 11.2 12.5947 11.2324 12.4786 11.2945C12.3625 11.3566 12.2635 11.4464 12.1904 11.556L11.5408 12.532C11.3216 12.8606 11.0246 13.13 10.6763 13.3163C10.328 13.5026 9.93902 13.6 9.544 13.6H6.456C6.06091 13.5999 5.67193 13.5024 5.3236 13.3159C4.97526 13.1295 4.67835 12.8599 4.4592 12.5312L3.8096 11.556C3.73652 11.4464 3.63754 11.3566 3.52143 11.2945C3.40532 11.2324 3.27567 11.2 3.144 11.2H0.8C0.587827 11.2 0.384344 11.1157 0.234315 10.9657C0.0842854 10.8156 0 10.6121 0 10.4Z"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M3.9472 0H12.0528C12.7616 0 13.3728 1.19209e-08 13.8624 0.0656C14.3864 0.136 14.8896 0.2952 15.2968 0.7032C15.7048 1.1112 15.864 1.6136 15.9344 2.1376C16 2.6272 16 3.2384 16 3.9472V12.0528C16 12.7616 16 13.3728 15.9344 13.8624C15.864 14.3864 15.7048 14.8896 15.2968 15.2968C14.8888 15.7048 14.3864 15.864 13.8624 15.9344C13.3728 16 12.7616 16 12.0528 16H3.9472C3.2384 16 2.6272 16 2.1376 15.9344C1.6136 15.864 1.1104 15.7048 0.7032 15.2968C0.2952 14.8888 0.136 14.3864 0.0656 13.8624C1.19209e-08 13.3728 0 12.7616 0 12.0528V3.9472C0 3.2384 1.19209e-08 2.6272 0.0656 2.1376C0.136 1.6136 0.2952 1.1104 0.7032 0.7032C1.1112 0.2952 1.6136 0.136 2.1376 0.0656C2.6272 1.19209e-08 3.2384 0 3.9472 0ZM2.352 1.6512C2.0048 1.6984 1.896 1.7736 1.8352 1.8344C1.7744 1.8952 1.6992 2.004 1.652 2.3512C1.6016 2.72 1.6 3.2232 1.6 4V12C1.6 12.7768 1.6016 13.2792 1.6512 13.6488C1.6984 13.996 1.7736 14.1048 1.8344 14.1656C1.8952 14.2264 2.004 14.3016 2.3512 14.3488C2.72 14.3984 3.2232 14.4 4 14.4H12C12.7768 14.4 13.2792 14.3984 13.6488 14.3488C13.996 14.3016 14.1048 14.2264 14.1656 14.1656C14.2264 14.1048 14.3016 13.996 14.3488 13.6488C14.3984 13.28 14.4 12.7768 14.4 12V4C14.4 3.2232 14.3984 2.7208 14.3488 2.3512C14.3016 2.004 14.2264 1.8952 14.1656 1.8344C14.1048 1.7736 13.996 1.6984 13.6488 1.6512C13.28 1.6016 12.7768 1.6 12 1.6H4C3.2232 1.6 2.7216 1.6016 2.352 1.6512Z"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M8.80001 9.20002V5.20002C8.80001 4.98785 8.71573 4.78437 8.5657 4.63434C8.41567 4.48431 8.21219 4.40002 8.00001 4.40002C7.78784 4.40002 7.58436 4.48431 7.43433 4.63434C7.2843 4.78437 7.20001 4.98785 7.20001 5.20002V9.20002C7.20001 9.4122 7.2843 9.61568 7.43433 9.76571C7.58436 9.91574 7.78784 10 8.00001 10C8.21219 10 8.41567 9.91574 8.5657 9.76571C8.71573 9.61568 8.80001 9.4122 8.80001 9.20002Z"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M5.43442 7.36559C5.58445 7.51556 5.78789 7.59981 6.00002 7.59981C6.21216 7.59981 6.4156 7.51556 6.56562 7.36559L8.00002 5.93119L9.43442 7.36559C9.58531 7.51131 9.78739 7.59195 9.99714 7.59012C10.2069 7.5883 10.4076 7.50417 10.5559 7.35584C10.7042 7.20751 10.7883 7.00686 10.7902 6.79711C10.792 6.58735 10.7114 6.38527 10.5656 6.23439L8.96562 4.63439C8.70953 4.3783 8.36219 4.23444 8.00002 4.23444C7.63786 4.23444 7.29052 4.3783 7.03442 4.63439L5.43442 6.23439C5.28445 6.38441 5.2002 6.58785 5.2002 6.79999C5.2002 7.01212 5.28445 7.21556 5.43442 7.36559Z"/>
    </svg>


  );
}

export default UnArchiveIcon;
