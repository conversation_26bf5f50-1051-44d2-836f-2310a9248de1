import React from 'react';

const JsonImportIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={height}
            height={width}
            viewBox="0 0 229 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M66 8C66 3.58172 69.5817 0 74 0H178C182.418 0 186 3.58172 186 8V92C186 96.4183 182.418 100 178 100H74C69.5817 100 66 96.4183 66 92V8Z"
                fill="#6637EC"
                fillOpacity="0.1"
            />
            <g filter="url(#filter0_d_5978_2907)">
                <g clipPath="url(#clip0_5978_2907)">
                    <path
                        d="M24 35.75C24 33.1266 26.1266 31 28.75 31H199.75C202.373 31 204.5 33.1266 204.5 35.75V64.25C204.5 66.8734 202.373 69 199.75 69H28.75C26.1267 69 24 66.8734 24 64.25V35.75Z"
                        fill="white"
                    />
                    <rect
                        x="20"
                        y="27"
                        width="46"
                        height="46"
                        rx="2.03571"
                        fill="#6637EC"
                        fillOpacity="0.73"
                    />
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M35.6911 48.9873H51.7508V44.5083H47.0009C46.503 44.5083 45.8076 44.2407 45.4792 43.9152C45.1508 43.5897 44.9495 43.0024 44.9495 42.5089V37.7409H32.8735C32.8313 37.7409 32.7995 37.7619 32.7784 37.7829C32.7466 37.8039 32.736 37.8354 32.736 37.8774V62.1329C32.736 62.1644 32.7572 62.2064 32.7784 62.2274C32.7995 62.2587 32.8419 62.2692 32.8735 62.2692H51.6128C51.6552 62.2692 51.6529 62.2484 51.6741 62.2274C51.7059 62.2064 51.7506 62.1644 51.7506 62.1329V59.5901H35.6909C34.7383 59.5901 33.9535 58.8152 33.9535 57.8683V50.7093C33.9535 49.7622 34.7355 48.9873 35.6911 48.9873ZM38.3717 51.4541H39.9659V54.5329C39.9659 55.1795 39.9142 55.6717 39.8106 56.0094C39.7064 56.346 39.497 56.6325 39.1824 56.8672C38.8675 57.1007 38.4639 57.2183 37.9736 57.2183C37.4538 57.2183 37.0517 57.1404 36.7671 56.9861C36.481 56.8315 36.2609 56.606 36.1054 56.3078C35.9496 56.0109 35.8577 55.6427 35.8301 55.2047L37.3451 54.9777C37.3474 55.2257 37.3678 55.4105 37.4047 55.5306C37.4419 55.652 37.5053 55.7484 37.5954 55.8235C37.6562 55.8725 37.7424 55.8962 37.8549 55.8962C38.0329 55.8962 38.1638 55.8235 38.2475 55.6784C38.3302 55.5331 38.3719 55.2878 38.3719 54.9434L38.3717 51.4541ZM40.7611 55.2482L42.2691 55.1438C42.3017 55.4128 42.3685 55.6188 42.4691 55.7587C42.6331 55.9871 42.8662 56.1018 43.1703 56.1018C43.3966 56.1018 43.5714 56.0438 43.6932 55.9262C43.8163 55.8091 43.8775 55.6728 43.8775 55.5186C43.8775 55.3723 43.8201 55.2402 43.7038 55.124C43.5879 55.0079 43.3175 54.8998 42.8927 54.7954C42.1958 54.6238 41.7004 54.3943 41.404 54.1079C41.1048 53.8229 40.9552 53.4586 40.9552 53.0152C40.9552 52.7248 41.0317 52.4506 41.1851 52.1918C41.3381 51.9319 41.568 51.7286 41.8756 51.581C42.1833 51.4331 42.6045 51.3592 43.1395 51.3592C43.7964 51.3592 44.2969 51.4938 44.6414 51.7643C44.9863 52.0333 45.1906 52.4623 45.2567 53.0509L43.7629 53.1486C43.7233 52.8913 43.6398 52.704 43.5127 52.5879C43.3845 52.4703 43.2086 52.4123 42.9851 52.4123C42.8005 52.4123 42.6607 52.4558 42.567 52.5417C42.4738 52.6276 42.4274 52.7332 42.4274 52.8571C42.4274 52.9468 42.4657 53.0274 42.5412 53.1001C42.6143 53.1755 42.7912 53.2439 43.0713 53.3086C43.7652 53.4737 44.2621 53.6413 44.5611 53.8099C44.8615 53.9789 45.0806 54.1875 45.2171 54.4382C45.3535 54.6877 45.4218 54.9674 45.4218 55.2773C45.4218 55.6404 45.3306 55.9754 45.1488 56.283C44.966 56.5892 44.7122 56.8227 44.3844 56.981C44.0579 57.1394 43.6459 57.2186 43.1484 57.2186C42.2746 57.2186 41.6693 57.0325 41.333 56.6616C40.9965 56.291 40.8058 55.8196 40.7611 55.2482ZM46.0316 54.2927C46.0316 53.3676 46.2651 52.6471 46.7327 52.1324C47.2007 51.6165 47.8519 51.3592 48.6871 51.3592C49.5427 51.3592 50.2021 51.6127 50.6649 52.1179C51.128 52.6247 51.3592 53.3347 51.3592 54.2467C51.3592 54.909 51.2586 55.4512 51.0562 55.875C50.8541 56.2988 50.561 56.6287 50.179 56.8647C49.7961 57.1007 49.3198 57.2183 48.7492 57.2183C48.1701 57.2183 47.69 57.1169 47.3097 56.9134C46.93 56.7089 46.6212 56.387 46.3854 55.9462C46.1496 55.5069 46.0316 54.9552 46.0316 54.2927ZM47.6184 54.2967C47.6184 54.8681 47.7152 55.2797 47.9082 55.5289C48.1017 55.7784 48.3653 55.9038 48.6977 55.9038C49.0389 55.9038 49.3048 55.7811 49.4912 55.5369C49.6792 55.2916 49.7726 54.8536 49.7726 54.2187C49.7726 53.6856 49.6745 53.2964 49.4794 53.0509C49.2832 52.804 49.0185 52.6814 48.6835 52.6814C48.3629 52.6814 48.1042 52.8067 47.9104 53.0562C47.7152 53.3057 47.6184 53.72 47.6184 54.2967ZM52.0485 51.4541H53.5315L55.4592 54.5841V51.4541H56.96V57.1232H55.4592L53.542 54.0102V57.1232H52.0485V51.4541ZM53.4831 48.9873H57.2625C58.2181 48.9873 59 49.7651 59 50.7093V57.8683C59 58.8124 58.2154 59.5901 57.2625 59.5901H53.4831V62.677C53.4831 63.0445 53.335 63.3698 53.0912 63.6115C52.8475 63.8528 52.5191 64 52.1483 64H32.3349C31.9641 64 31.6359 63.853 31.392 63.6115C31.1485 63.37 31 63.0445 31 62.677V37.3335C31 36.966 31.1483 36.6405 31.392 36.399C31.6357 36.1575 31.9747 36.0105 32.3349 36.0105H45.8347C45.8665 36 45.8983 36 45.9301 36C46.0784 36 46.2267 36.063 46.3327 36.1573H46.3538C46.375 36.1678 46.3856 36.1783 46.4068 36.1993L53.3028 43.1181C53.4194 43.2336 53.5041 43.3911 53.5041 43.5696C53.5041 43.6221 53.4935 43.6641 53.4829 43.7166L53.4831 48.9873ZM46.5236 42.373V38.037L51.4495 42.9818H47.1378C46.9685 42.9818 46.8202 42.9083 46.7035 42.8035C46.5977 42.6985 46.5236 42.541 46.5236 42.373Z"
                        fill="white"
                    />
                    <path
                        d="M75 45C75 43.8954 75.8954 43 77 43H186C187.105 43 188 43.8954 188 45V45C188 46.1046 187.105 47 186 47H77C75.8954 47 75 46.1046 75 45V45Z"
                        fill="#B39BF6"
                    />
                    <path
                        d="M75 55C75 53.8954 75.8954 53 77 53H152C153.105 53 154 53.8954 154 55V55C154 56.1046 153.105 57 152 57H77C75.8954 57 75 56.1046 75 55V55Z"
                        fill="#B39BF6"
                    />
                </g>
            </g>
            <defs>
                <filter
                    id="filter0_d_5978_2907"
                    x="0.25"
                    y="9.625"
                    width="228"
                    height="85.5"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                    />
                    <feOffset dy="2.375" />
                    <feGaussianBlur stdDeviation="11.875" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
                    />
                    <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_5978_2907"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_5978_2907"
                        result="shape"
                    />
                </filter>
                <clipPath id="clip0_5978_2907">
                    <path
                        d="M24 35.75C24 33.1266 26.1266 31 28.75 31H199.75C202.373 31 204.5 33.1266 204.5 35.75V64.25C204.5 66.8734 202.373 69 199.75 69H28.75C26.1267 69 24 66.8734 24 64.25V35.75Z"
                        fill="white"
                    />
                </clipPath>
            </defs>
        </svg>
    );
};

export default JsonImportIcon;



