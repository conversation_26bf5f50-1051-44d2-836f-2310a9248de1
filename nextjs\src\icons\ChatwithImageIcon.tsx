import React from 'react';

const ChatwithImage = ({ width, height, className }: any) => {
    return (
        <svg
            width={width}
            height={height}
            className={className}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H16.6667C18.5076 0 20 1.49238 20 3.33333V16.6667C20 18.5076 18.5076 20 16.6667 20H3.33333C1.49238 20 0 18.5076 0 16.6667V3.33333Z"
                fill="#BFDFF6"
            />
            <path
                d="M14.1367 11.7891L12.082 9.73535L7.68066 14.1367H13.6445C13.9161 14.1367 14.1367 13.9161 14.1367 13.6445V11.7891ZM8.40723 8.17578C8.40705 8.04822 8.30339 7.94531 8.17578 7.94531C8.04832 7.94549 7.94549 8.04832 7.94531 8.17578C7.94531 8.30339 8.04822 8.40705 8.17578 8.40723C8.3035 8.40723 8.40723 8.3035 8.40723 8.17578ZM14.1367 6.35352C14.1367 6.08198 13.9161 5.86133 13.6445 5.86133H6.35352C6.08198 5.86133 5.86133 6.08198 5.86133 6.35352V13.6445C5.86133 13.8486 5.98609 14.0233 6.16309 14.0977L11.6934 8.56836L11.7793 8.49805C11.9928 8.35685 12.2836 8.38032 12.4717 8.56836L14.1367 10.2334V6.35352ZM9.50781 8.17578C9.50781 8.91101 8.91101 9.50781 8.17578 9.50781C7.4407 9.50764 6.84473 8.9109 6.84473 8.17578C6.8449 7.44081 7.44081 6.8449 8.17578 6.84473C8.9109 6.84473 9.50764 7.4407 9.50781 8.17578ZM15.2363 13.6445C15.2363 14.5236 14.5236 15.2363 13.6445 15.2363H6.35352C5.47446 15.2363 4.76172 14.5236 4.76172 13.6445V6.35352C4.76172 5.47446 5.47446 4.76172 6.35352 4.76172H13.6445C14.5236 4.76172 15.2363 5.47446 15.2363 6.35352V13.6445Z"
                fill="#3B7BC4"
            />
        </svg>
    );
};

export default ChatwithImage;
