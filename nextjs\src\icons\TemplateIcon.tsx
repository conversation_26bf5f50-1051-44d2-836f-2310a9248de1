import React from 'react';

const TemplateIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 133 160"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.8785 129.626C13.5418 129.626 11.2413 130.073 9.17795 130.958V10.2138C9.17795 9.69224 9.5804 9.08252 10.5899 9.08252H122.48C123.49 9.08252 123.891 9.69213 123.891 10.2122V128.244C123.884 129.007 123.26 129.628 122.485 129.626H15.8785ZM0.0691397 10.2138V144.134L0.0678753 144.14C0.0202836 144.347 0 144.549 0 144.74C0 153.295 7.19792 160 15.8785 160H114.343C120.003 160 125.143 155.682 125.143 149.788V138.37C129.649 137.199 132.983 133.125 133 128.263V10.2129C133 4.43778 128.268 0 122.48 0H10.5899C4.80121 0 0.0691397 4.43849 0.0691397 10.2138ZM9.04049 144.812L9.04044 144.816C9.06329 148.042 11.9309 150.917 15.8785 150.917H114.343C115.577 150.917 116.034 150.106 116.034 149.788V138.709H15.8785C11.9476 138.709 9.07948 141.574 9.04074 144.791L9.04049 144.812Z"
            />
            <rect
                x="84.7598"
                y="38"
                width="10"
                height="75"
                rx="5"
                transform="rotate(32.9194 84.7598 38)"
            />
        </svg>
    );
};

export default TemplateIcon;
