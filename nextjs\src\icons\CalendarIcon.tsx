import React from 'react';

const CalendarIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 85 88"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M11.7039 88H73.2961C79.7528 88 85 82.7688 85 76.3318V18.1866C85 11.7496 79.7528 6.5184 73.2961 6.5184H68.6539V3.2592C68.6539 1.46658 67.1825 0 65.3844 0C63.5864 0 62.1153 1.46658 62.1153 3.2592V6.5184H22.8847V3.2592C22.8847 1.46658 21.4134 0 19.6153 0C17.8172 0 16.3461 1.46658 16.3461 3.2592V6.5184H11.7039C5.24716 6.5184 0 11.7496 0 18.1866V76.3318C0 82.7688 5.24716 88 11.7039 88ZM6.53861 18.1866C6.53861 15.3511 8.85973 13.0371 11.7039 13.0371H16.3461V16.2963C16.3461 18.0889 17.8172 19.5555 19.6153 19.5555C21.4134 19.5555 22.8844 18.0889 22.8844 16.2963V13.0371H62.1153V16.2963C62.1153 18.0889 63.5864 19.5555 65.3844 19.5555C67.1825 19.5555 68.6536 18.0889 68.6536 16.2963V13.0371H73.2959C76.14 13.0371 78.4611 15.3511 78.4611 18.1866V33.2788H6.53861V18.1866ZM6.53861 39.7972H78.4617V76.3318C78.4617 79.1673 76.1405 81.4813 73.2964 81.4813H11.7039C8.85973 81.4813 6.53861 79.1673 6.53861 76.3318V39.7972Z" />
        </svg>
    );
};

export default CalendarIcon;
