import React from 'react';

const UploadIcon = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={height}
            height={width}
            viewBox="0 0 60 61"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="30" cy="30.8418" r="30" fill="#E6E6E6" />
            <path
                d="M37.4809 24.6491C37.0661 22.991 36.1093 21.519 34.7623 20.4669C33.4153 19.4148 31.7555 18.8428 30.0463 18.8418C28.3372 18.8408 26.6766 19.4109 25.3285 20.4614C23.9803 21.512 23.0217 22.9828 22.605 24.6404C20.6393 24.8249 18.8202 25.7595 17.5256 27.2502C16.231 28.7408 15.5603 30.6728 15.653 32.645C15.7456 34.6172 16.5945 36.4778 18.0231 37.8405C19.4518 39.2032 21.3505 39.9632 23.3248 39.9626H26.204C26.4586 39.9626 26.7027 39.8615 26.8827 39.6815C27.0626 39.5015 27.1638 39.2574 27.1638 39.0029C27.1638 38.7483 27.0626 38.5042 26.8827 38.3242C26.7027 38.1442 26.4586 38.0431 26.204 38.0431H23.3248C22.5686 38.0453 21.8194 37.8986 21.1199 37.6112C20.4204 37.3239 19.7844 36.9016 19.2481 36.3684C18.165 35.2916 17.5541 33.8287 17.5496 32.3015C17.5452 30.7743 18.1476 29.3078 19.2243 28.2248C20.3011 27.1417 21.764 26.5308 23.2912 26.5263C23.5375 26.5448 23.7818 26.4712 23.9769 26.3198C24.172 26.1684 24.3038 25.9499 24.3469 25.7067C24.5424 24.3362 25.2256 23.0823 26.2713 22.1752C27.317 21.2681 28.6548 20.7686 30.0391 20.7686C31.4234 20.7686 32.7613 21.2681 33.8069 22.1752C34.8526 23.0823 35.5359 24.3362 35.7313 25.7067C35.7815 25.9414 35.9116 26.1514 36.0994 26.3009C36.2872 26.4504 36.5211 26.5301 36.7611 26.5263C38.2883 26.5263 39.753 27.133 40.8329 28.2129C41.9128 29.2928 42.5195 30.7575 42.5195 32.2847C42.5195 33.8119 41.9128 35.2766 40.8329 36.3565C39.753 37.4364 38.2883 38.0431 36.7611 38.0431H33.8819C33.6274 38.0431 33.3832 38.1442 33.2033 38.3242C33.0233 38.5042 32.9222 38.7483 32.9222 39.0029C32.9222 39.2574 33.0233 39.5015 33.2033 39.6815C33.3832 39.8615 33.6274 39.9626 33.8819 39.9626H36.7611C38.721 39.9421 40.5988 39.1728 42.0098 37.8124C43.4208 36.452 44.2581 34.6035 44.3502 32.6456C44.4422 30.6878 43.782 28.7689 42.5048 27.2821C41.2276 25.7954 39.4302 24.8533 37.4809 24.6491Z"
                fill="#8C8C8C"
            />
            <path
                d="M34.1636 32.9633C34.3446 33.1382 34.5871 33.2349 34.8387 33.2327C35.0904 33.2305 35.3311 33.1296 35.509 32.9517C35.687 32.7737 35.7879 32.533 35.7901 32.2814C35.7923 32.0297 35.6955 31.7873 35.5207 31.6063L30.722 26.8076C30.542 26.6277 30.298 26.5266 30.0435 26.5266C29.789 26.5266 29.5449 26.6277 29.365 26.8076L24.5663 31.6063C24.3915 31.7873 24.2947 32.0297 24.2969 32.2814C24.2991 32.533 24.4 32.7737 24.578 32.9517C24.7559 33.1296 24.9966 33.2305 25.2483 33.2327C25.4999 33.2349 25.7423 33.1382 25.9233 32.9633L29.0838 29.8029V41.8822C29.0838 42.1367 29.1849 42.3808 29.3649 42.5608C29.5448 42.7408 29.789 42.8419 30.0435 42.8419C30.298 42.8419 30.5421 42.7408 30.7221 42.5608C30.9021 42.3808 31.0032 42.1367 31.0032 41.8822V29.8029L34.1636 32.9633Z"
                fill="#8C8C8C"
            />
        </svg>
    );
};

export default UploadIcon;
