import React from 'react';

const UserSetting = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.2861 0C7.4557 0 5.14304 2.31265 5.14304 5.14304C5.14304 7.97343 7.4557 10.2861 10.2861 10.2861C13.1165 10.2861 15.4291 7.97343 15.4291 5.14304C15.4291 2.31265 13.1165 0 10.2861 0ZM10.2861 1.71435C12.1899 1.71435 13.7148 3.24012 13.7148 5.14304C13.7148 7.04682 12.1899 8.57173 10.2861 8.57173C8.3823 8.57173 6.85739 7.04682 6.85739 5.14304C6.85739 3.24012 8.3823 1.71435 10.2861 1.71435ZM7.71456 12.0004C3.45527 12.0004 0 15.4557 0 19.715V21.4293C0 21.6567 0.0903091 21.8747 0.25106 22.0354C0.411811 22.1962 0.629837 22.2865 0.857174 22.2865H10.1558C10.3831 22.2865 10.6012 22.1962 10.7619 22.0354C10.9227 21.8747 11.013 21.6567 11.013 21.4293C11.013 21.202 10.9227 20.984 10.7619 20.8232C10.6012 20.6625 10.3831 20.5722 10.1558 20.5722H1.71435V19.715C1.71435 16.3763 4.37587 13.7148 7.71456 13.7148H11.157C11.3843 13.7148 11.6023 13.6245 11.7631 13.4637C11.9238 13.303 12.0141 13.0849 12.0141 12.8576C12.0141 12.6303 11.9238 12.4122 11.7631 12.2515C11.6023 12.0907 11.3843 12.0004 11.157 12.0004H7.71456ZM17.9895 12.0004C17.8767 12.0017 17.7652 12.0251 17.6615 12.0695C17.5577 12.1139 17.4637 12.1783 17.3849 12.259C17.3061 12.3397 17.244 12.4352 17.2021 12.54C17.1602 12.6448 17.1394 12.7568 17.1409 12.8696V13.8005C16.6038 13.9124 16.0933 14.1269 15.6374 14.4322L14.9791 13.7739C14.7768 13.5665 14.5479 13.4876 14.3311 13.4996C13.6822 13.5339 13.1439 14.3799 13.7671 14.986L14.4279 15.6477C14.1242 16.1036 13.9112 16.6138 13.8005 17.1503H12.8696C11.7107 17.134 11.7107 18.8818 12.8696 18.8647H13.8039C13.9154 19.4056 14.1339 19.9087 14.4297 20.3553L13.7636 21.0213C12.9322 21.8296 14.1674 23.0648 14.9757 22.2334L15.6408 21.5691C16.0959 21.873 16.6052 22.0866 17.1409 22.1982V23.1308C17.1238 24.2897 18.8715 24.2897 18.8552 23.1308V22.2008C19.39 22.0895 19.8984 21.8765 20.3527 21.5733L21.023 22.2419C21.1013 22.327 21.1959 22.3953 21.3013 22.4428C21.4066 22.4903 21.5205 22.516 21.636 22.5184C21.7515 22.5208 21.8663 22.4998 21.9735 22.4567C22.0808 22.4136 22.1781 22.3493 22.2598 22.2676C22.3416 22.1859 22.4059 22.0885 22.449 21.9813C22.4921 21.874 22.5131 21.7592 22.5107 21.6437C22.5083 21.5282 22.4826 21.4143 22.4351 21.309C22.3876 21.2036 22.3192 21.109 22.2342 21.0308L21.5682 20.3639C21.8719 19.9096 22.0855 19.4012 22.1974 18.8664H23.1317C23.359 18.8664 23.577 18.7761 23.7378 18.6153C23.8986 18.4546 23.9889 18.2366 23.9889 18.0092C23.9889 17.7819 23.8986 17.5639 23.7378 17.4031C23.577 17.2423 23.359 17.152 23.1317 17.152H22.2008C22.0902 16.612 21.8759 16.0985 21.5699 15.64L22.2308 14.9791C22.3131 14.8995 22.3786 14.8041 22.4233 14.6987C22.468 14.5933 22.4911 14.4799 22.4911 14.3654C22.4911 14.2508 22.468 14.1375 22.4233 14.032C22.3786 13.9266 22.3131 13.8312 22.2308 13.7516C22.0681 13.5931 21.8494 13.5053 21.6222 13.5073C21.3957 13.5112 21.18 13.6046 21.0222 13.7671L20.3596 14.4297C19.9041 14.1254 19.3942 13.9118 18.8578 13.8005V12.8696C18.8597 12.7552 18.8385 12.6417 18.7957 12.5356C18.7529 12.4296 18.6893 12.3332 18.6086 12.2521C18.5278 12.1711 18.4317 12.1071 18.3258 12.0638C18.2199 12.0206 18.1064 11.999 17.9921 12.0004H17.9895ZM18.0006 15.4291C19.4304 15.4291 20.5722 16.57 20.5722 18.0006C20.5722 19.4304 19.4304 20.5722 18.0006 20.5722C16.57 20.5722 15.4291 19.4304 15.4291 18.0006C15.4291 16.57 16.57 15.4291 18.0006 15.4291Z"/>
</svg>
  );
}

export default UserSetting;
