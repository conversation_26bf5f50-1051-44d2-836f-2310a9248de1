const GoogleDriveIcon = ({ className }) => {
    return (
        <svg
            className={className}
            width="21"
            height="21"
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_6651_202)">
                <path
                    d="M14.0803 1.40674H6.91969L0 13.392L3.58029 19.5932H17.4198L21 13.392L14.0803 1.40674ZM13.3698 2.6372L19.2239 12.7768H14.1947L8.34055 2.6372H13.3698ZM12.7738 12.7768H8.22616L10.5 8.83847L12.7738 12.7768ZM1.42086 13.392L7.27489 3.25248L9.78955 7.608L3.93548 17.7475L1.42086 13.392ZM16.7093 18.3628H5.00111L7.51573 14.0073H19.2239L16.7093 18.3628Z"
                    fill="url(#paint0_linear_6651_202)"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_6651_202"
                    x1="19.8882"
                    y1="17.7882"
                    x2="-1.23529"
                    y2="-1.97651"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#0085F7" />
                    <stop offset="1" stopColor="#004E91" />
                </linearGradient>
                <clipPath id="clip0_6651_202">
                    <rect width="21" height="21" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default GoogleDriveIcon;
