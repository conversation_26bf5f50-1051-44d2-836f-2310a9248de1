[{"Category": "SEO", "Checklist Item": "Canonical tag (<link rel=\"canonical\">) present", "Prompt": "Ensure canonical tags are present to prevent duplicate content issues."}, {"Category": "Accessibility", "Checklist Item": "Check for missing alt attributes. Flag <img> tags without alt attribute.", "Prompt": "Ensure all images have descriptive alt tags for accessibility and image SEO."}, {"Category": "Accessibility", "Checklist Item": "Check heading hierarchy (H1, H2, H3). Ensure logical heading order.", "Prompt": "Ensure that headings follow semantic order: one <h1>, followed by properly nested <h2>s."}, {"Category": "Accessibility", "Checklist Item": "Images have alt attributes", "Prompt": "Ensure every <img> tag has a descriptive 'alt' attribute."}, {"Category": "Accessibility", "Checklist Item": "ARIA attributes or landmarks", "Prompt": "Verify the use of ARIA roles and landmarks for better screen reader support."}, {"Category": "Accessibility", "Checklist Item": "<noscript> tag provides fallback content", "Prompt": "Ensure a <noscript> tag exists"}, {"Category": "UX", "Checklist Item": "External links open in a new tab (target='_blank' rel='noopener noreferrer')", "Prompt": "Ensure all external <a> links use target='_blank' and include rel='noopener noreferrer' for security and user experience."}, {"Category": "Security", "Checklist Item": "Ensure each font-family declaration has a fallback font (e.g., sans-serif).", "Prompt": "Flag large inline <script> blocks over 50KB. Recommend moving them to external files or splitting them up."}, {"Category": "Security", "Checklist Item": "Detect non-secure JS sources. Find <script> tags using http:// instead of https://.", "Prompt": "Check all <script> tags to ensure they use HTTPS. Flag any insecure HTTP sources."}, {"Category": "Security", "Checklist Item": "HTTPS is enforced for all resources", "Prompt": "Ensure all resource URLs (scripts, styles, images) are loaded over HTTPS."}, {"Category": "Security", "Checklist Item": "No visible API keys or sensitive data in page source", "Prompt": "Ensure no visible API keys, secrets, or tokens are present in the HTML or JS source."}, {"Category": "Security", "Checklist Item": "Check if Google reCAPTCHA is used when a form is present", "Prompt": "Check if the page includes Google reCAPTCHA script when a form is detected. If reCAPTCHA Exists than Pass and if it doesn’t exist than Fail"}, {"Category": "Code Quality", "Checklist Item": "Detect duplicate script includes", "Prompt": "Scan for duplicate <script src=''> URLs and flag repeated inclusions."}, {"Category": "Code Quality", "Checklist Item": "Avoid use of !important in CSS", "Prompt": "Ensure '!important' is not used in styles unless absolutely necessary."}, {"Category": "Code Quality", "Checklist Item": "Avoid inline styles", "Prompt": "Check if inline 'style=' attributes are used and flag if excessive."}, {"Category": "Code Quality", "Checklist Item": "No duplicate or unnecessary IDs in the DOM", "Prompt": "Check the DOM for repeated id attributes. Each ID must be unique to avoid conflicts and accessibility issues."}, {"Category": "Content Quality", "Checklist Item": "Scan visible text for grammar and spelling errors.", "Prompt": "Review the page for grammar and spelling errors to ensure content professionalism."}, {"Category": "Responsive", "Checklist Item": "Ensure viewport is set for mobile devices.", "Prompt": "Make sure the page has a viewport meta tag to ensure mobile responsiveness."}, {"Category": "HTML Best Practices", "Checklist Item": "Check for incorrect punctuation (apostrophes, quotes, hyphens/dashes)", "Prompt": "Ensure that text content on the page uses correct punctuation:\n\nCurly quotes (“ ” and ‘ ’) instead of straight quotes (\" and ')\n\nProper apostrophes (’ instead of ')\n\nEn dashes (–) and em dashes (—) instead of hyphens (-) when contextually appropriate"}, {"Category": "Compliance", "Checklist Item": "Check if Terms link exists. Search for footer link to terms page.", "Prompt": "Ensure a Terms of Service or Terms & Conditions page is linked."}, {"Category": "Compliance", "Checklist Item": "Check for cookie consent banner. Detect cookie banner or consent script.", "Prompt": "Verify that a cookie consent popup appears on first visit and handles consent properly."}, {"Category": "Analytics", "Checklist Item": "Check for Facebook Pixel script", "Prompt": "Ensure that Facebook Pixel tracking script is loaded if used for retargeting."}, {"Category": "Analytics", "Checklist Item": "Check if Google Analytics script is present", "Prompt": "Check the page source for the presence of the Google Analytics tracking script. Look for a script tag that loads https://www.googletagmanager.com/gtag/js and a gtag('config', 'G-XXXXXXXXXX') call in the following script block. If not found, flag it as missing."}, {"Category": "Performance", "Checklist Item": "Check if favicon is present. Verify <link rel='icon'> exists.", "Prompt": "Verify that a favicon is set for better user experience and brand recognition."}, {"Category": "Performance", "Checklist Item": "Check if scripts are minified. Verify .js filenames and look for compact, whitespace-free code.", "Prompt": "Verify that JavaScript files are minified (e.g., .min.js, no line breaks or whitespace)."}, {"Category": "Performance", "Checklist Item": "Lazy loading of images", "Prompt": "Verify that images use lazy loading to defer offscreen image loading."}, {"Category": "Performance", "Checklist Item": "Ensure defer or async used on scripts", "Prompt": "Confirm all JavaScript files use defer or async to avoid blocking page rendering."}, {"Category": "Performance", "Checklist Item": "Lazy loading is enabled for images", "Prompt": "Ensure that <img> tags include 'loading=\"lazy\"' for performance optimization."}, {"Category": "Performance", "Checklist Item": "Ensure scripts use async or defer attributes", "Prompt": "Check that all external <script> tags use 'async' or 'defer'."}, {"Category": "Performance", "Checklist Item": "Count third-party script usage", "Prompt": "Count external <script> tags from third-party domains (Google, FB, etc.). Flag if excessive."}]