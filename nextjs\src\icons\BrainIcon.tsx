import React from 'react';

const BrainIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.28999 9.77734C5.81861 9.77734 5.4375 10.1585 5.4375 10.6298C5.4375 11.1012 5.81861 11.4823 6.28999 11.4823C7.10236 11.4823 7.77431 12.1743 7.77431 13.0168C7.77431 13.8593 7.11238 14.5513 6.28999 14.5513C5.46759 14.5513 5.4375 14.9324 5.4375 15.4038C5.4375 15.8751 5.81861 16.2562 6.28999 16.2562C8.04511 16.2562 9.47928 14.802 9.47928 13.0168C9.47928 11.2316 8.04511 9.77734 6.28999 9.77734Z" fill="#0D0D0D"/>
    <path d="M24.4676 13.1534C24.4676 11.4986 23.4747 10.0845 22.0405 9.51282C22.1909 9.08156 22.2712 8.63025 22.2712 8.1689C22.2712 6.31349 20.9975 4.74893 19.2825 4.34776C19.112 2.35194 17.4571 0.777344 15.4513 0.777344C13.4454 0.777344 13.1646 1.34901 12.4525 2.24162C11.7505 1.34901 10.6673 0.777344 9.4538 0.777344C7.44795 0.777344 5.80314 2.35194 5.62262 4.34776C3.91765 4.74893 2.63391 6.30346 2.63391 8.1689C2.63391 10.0343 2.71414 9.08156 2.86457 9.51282C1.44042 10.0845 0.4375 11.4986 0.4375 13.1534C0.4375 14.8083 1.3301 16.072 2.65396 16.7038C2.47344 17.1551 2.38318 17.6465 2.38318 18.138C2.38318 20.0636 3.76721 21.6683 5.59254 21.9992C6.11406 23.624 7.61845 24.7773 9.34348 24.7773C11.0685 24.7773 11.7104 24.1756 12.4325 23.2429C13.1546 24.1756 14.2779 24.7773 15.5215 24.7773C16.7651 24.7773 18.7509 23.624 19.2724 21.9992C21.0978 21.6683 22.4818 20.0636 22.4818 18.138C22.4818 16.2124 22.3915 17.1551 22.211 16.7038C23.5349 16.072 24.4275 14.708 24.4275 13.1534H24.4676ZM11.6101 20.7556C11.6101 22.0293 10.6072 23.0724 9.37356 23.0724C8.13996 23.0724 7.3276 22.2299 7.1571 21.1167C7.1571 21.1167 7.1571 21.1066 7.1571 21.0966C7.13704 20.9863 7.13704 20.876 7.13704 20.7656C7.13704 19.4919 8.13996 18.4489 9.37356 18.4489C10.6072 18.4489 10.226 18.0678 10.226 17.5964C10.226 17.125 9.84494 16.7439 9.37356 16.7439C7.38777 16.7439 5.74297 18.2483 5.47218 20.194C4.6899 19.853 4.11823 19.0607 4.11823 18.158C4.11823 17.2554 4.28873 17.1451 4.60967 16.7439C4.80022 16.5032 4.85037 16.1722 4.74005 15.8914C4.62973 15.6106 4.35893 15.4 4.05806 15.3699C2.98493 15.2395 2.17257 14.2968 2.17257 13.1735C2.17257 12.0502 3.06516 11.0172 4.21852 10.9671C4.53946 10.957 4.82028 10.7564 4.95066 10.4656C5.08104 10.1748 5.04093 9.83376 4.84034 9.58302C4.52944 9.18186 4.35893 8.70045 4.35893 8.18896C4.35893 7.19606 4.99078 6.35361 5.86332 6.07279C6.4049 7.517 7.77891 8.52995 9.35351 8.52995C10.9281 8.52995 10.206 8.14884 10.206 7.67747C10.206 7.20609 9.82488 6.82498 9.35351 6.82498C8.34055 6.82498 7.47804 6.0427 7.34766 5.00969V4.98963C7.33763 4.89937 7.32759 4.8091 7.32759 4.71884C7.32759 3.49527 8.2904 2.50237 9.46383 2.50237C10.6372 2.50237 11.6001 3.49527 11.6001 4.71884C11.6001 5.94241 11.6001 4.77902 11.6001 4.79907V20.7757L11.6101 20.7556ZM20.8771 15.3498C20.5662 15.39 20.3054 15.5905 20.1951 15.8714C20.0848 16.1522 20.1249 16.4832 20.3155 16.7239C20.6364 17.115 20.8069 17.6265 20.8069 18.138C20.8069 19.0406 20.2352 19.8429 19.453 20.1739C19.1822 18.2282 17.5374 16.7239 15.5516 16.7239C13.5658 16.7239 14.6991 17.105 14.6991 17.5763C14.6991 18.0477 15.0802 18.4288 15.5516 18.4288C16.7852 18.4288 17.7881 19.4719 17.7881 20.7456C17.7881 22.0193 17.7881 20.9662 17.7681 21.0765C17.7681 21.0866 17.7681 21.1066 17.7681 21.1167C17.5976 22.2199 16.6548 23.0523 15.5616 23.0523C14.4684 23.0523 13.3251 22.0093 13.3251 20.7356C13.3251 19.4618 13.3251 20.7255 13.3251 20.7155V4.75895C13.3251 4.72887 13.3251 4.70881 13.3251 4.67872C13.3251 3.45515 14.2879 2.46226 15.4613 2.46226C16.6347 2.46226 17.5975 3.45515 17.5975 4.67872C17.5975 5.90229 17.5976 4.84922 17.5775 4.93948C17.5775 4.94951 17.5775 4.95954 17.5775 4.9796C17.4471 6.01261 16.5846 6.79489 15.5716 6.79489C14.5587 6.79489 14.7192 7.17601 14.7192 7.64738C14.7192 8.11876 15.1003 8.49987 15.5716 8.49987C17.1462 8.49987 18.5202 7.48691 19.0618 6.0427C19.9344 6.32352 20.5662 7.16598 20.5662 8.15887C20.5662 9.15177 20.3957 9.1618 20.0848 9.55294C19.8842 9.80367 19.8441 10.1447 19.9745 10.4355C20.1049 10.7264 20.3957 10.9169 20.7066 10.937C21.8499 10.9871 22.7526 11.96 22.7526 13.1434C22.7526 14.3269 21.9402 15.2094 20.8671 15.3398L20.8771 15.3498Z"/>
    <path d="M18.6268 11.4823C19.0982 11.4823 19.4793 11.1012 19.4793 10.6298C19.4793 10.1585 19.0982 9.77734 18.6268 9.77734C16.8717 9.77734 15.4375 11.2316 15.4375 13.0168C15.4375 14.802 16.8717 16.2562 18.6268 16.2562C20.3819 16.2562 19.4793 15.8751 19.4793 15.4038C19.4793 14.9324 19.0982 14.5513 18.6268 14.5513C17.8144 14.5513 17.1425 13.8593 17.1425 13.0168C17.1425 12.1743 17.8044 11.4823 18.6268 11.4823Z"/>
    </svg>
  );
}

export default BrainIcon;

