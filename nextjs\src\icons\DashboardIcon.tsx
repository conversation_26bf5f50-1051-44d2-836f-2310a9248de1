import React from 'react';

const DashboardIcon = ({ height, width, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path d="M6.57574 11.6061H2.81817C2.07075 11.6061 1.35393 11.903 0.825423 12.4315C0.296914 12.96 0 13.6768 0 14.4243V18.1818C0 18.9293 0.296914 19.6461 0.825423 20.1746C1.35393 20.7031 2.07075 21 2.81817 21H6.57574C7.32316 21 8.03997 20.7031 8.56848 20.1746C9.09699 19.6461 9.39391 18.9293 9.39391 18.1818V14.4243C9.39391 13.6768 9.09699 12.96 8.56848 12.4315C8.03997 11.903 7.32316 11.6061 6.57574 11.6061ZM2.81817 19.1212C2.56903 19.1212 2.33009 19.0222 2.15392 18.8461C1.97775 18.6699 1.87878 18.431 1.87878 18.1818V14.4243C1.87878 14.1751 1.97775 13.9362 2.15392 13.76C2.33009 13.5838 2.56903 13.4849 2.81817 13.4849H6.57574C6.82488 13.4849 7.06381 13.5838 7.23998 13.76C7.41615 13.9362 7.51513 14.1751 7.51513 14.4243V18.1818C7.51513 18.431 7.41615 18.6699 7.23998 18.8461C7.06381 19.0222 6.82488 19.1212 6.57574 19.1212H2.81817ZM17.8484 11.6061H14.0909C13.3434 11.6061 12.6266 11.903 12.0981 12.4315C11.5696 12.96 11.2727 13.6768 11.2727 14.4243V18.1818C11.2727 18.9293 11.5696 19.6461 12.0981 20.1746C12.6266 20.7031 13.3434 21 14.0909 21H17.8484C18.5958 21 19.3127 20.7031 19.8412 20.1746C20.3697 19.6461 20.6666 18.9293 20.6666 18.1818V14.4243C20.6666 13.6768 20.3697 12.96 19.8412 12.4315C19.3127 11.903 18.5958 11.6061 17.8484 11.6061ZM14.0909 19.1212C13.8417 19.1212 13.6028 19.0222 13.4266 18.8461C13.2504 18.6699 13.1515 18.431 13.1515 18.1818V14.4243C13.1515 14.1751 13.2504 13.9362 13.4266 13.76C13.6028 13.5838 13.8417 13.4849 14.0909 13.4849H17.8484C18.0976 13.4849 18.3365 13.5838 18.5127 13.76C18.6888 13.9362 18.7878 14.1751 18.7878 14.4243V18.1818C18.7878 18.431 18.6888 18.6699 18.5127 18.8461C18.3365 19.0222 18.0976 19.1212 17.8484 19.1212H14.0909ZM6.57574 0.333405H2.81817C2.07075 0.333405 1.35393 0.630318 0.825423 1.15883C0.296914 1.68734 0 2.40415 0 3.15158V6.90914C0 7.65656 0.296914 8.37338 0.825423 8.90189C1.35393 9.4304 2.07075 9.72731 2.81817 9.72731H6.57574C7.32316 9.72731 8.03997 9.4304 8.56848 8.90189C9.09699 8.37338 9.39391 7.65656 9.39391 6.90914V3.15158C9.39391 2.40415 9.09699 1.68734 8.56848 1.15883C8.03997 0.630318 7.32316 0.333405 6.57574 0.333405ZM2.81817 7.84853C2.56903 7.84853 2.33009 7.74956 2.15392 7.57339C1.97775 7.39722 1.87878 7.15828 1.87878 6.90914V3.15158C1.87878 2.90244 1.97775 2.6635 2.15392 2.48733C2.33009 2.31116 2.56903 2.21218 2.81817 2.21218H6.57574C6.82488 2.21218 7.06381 2.31116 7.23998 2.48733C7.41615 2.6635 7.51513 2.90244 7.51513 3.15158V6.90914C7.51513 7.15828 7.41615 7.39722 7.23998 7.57339C7.06381 7.74956 6.82488 7.84853 6.57574 7.84853H2.81817ZM17.8484 0.333405H14.0909C13.3434 0.333405 12.6266 0.630318 12.0981 1.15883C11.5696 1.68734 11.2727 2.40415 11.2727 3.15158V6.90914C11.2727 7.65656 11.5696 8.37338 12.0981 8.90189C12.6266 9.4304 13.3434 9.72731 14.0909 9.72731H17.8484C18.5958 9.72731 19.3127 9.4304 19.8412 8.90189C20.3697 8.37338 20.6666 7.65656 20.6666 6.90914V3.15158C20.6666 2.40415 20.3697 1.68734 19.8412 1.15883C19.3127 0.630318 18.5958 0.333405 17.8484 0.333405ZM14.0909 7.84853C13.8417 7.84853 13.6028 7.74956 13.4266 7.57339C13.2504 7.39722 13.1515 7.15828 13.1515 6.90914V3.15158C13.1515 2.90244 13.2504 2.6635 13.4266 2.48733C13.6028 2.31116 13.8417 2.21218 14.0909 2.21218H17.8484C18.0976 2.21218 18.3365 2.31116 18.5127 2.48733C18.6888 2.6635 18.7878 2.90244 18.7878 3.15158V6.90914C18.7878 7.15828 18.6888 7.39722 18.5127 7.57339C18.3365 7.74956 18.0976 7.84853 17.8484 7.84853H14.0909Z" />
        </svg>
    );
};

export default DashboardIcon;
