const ViewIcon = ({ height, width, className }:any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 118 85"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M58.9898 9.10714C45.634 9.10714 34.6544 14.7232 26.2353 21.952C18.3488 28.7444 12.9 36.808 10.1141 42.5C12.9 48.192 18.3488 56.2556 26.2148 63.048C34.6544 70.2768 45.634 75.8929 58.9898 75.8929C72.3455 75.8929 83.3252 70.2768 91.7442 63.048C99.6307 56.2556 105.08 48.192 107.865 42.5C105.08 36.808 99.6307 28.7444 91.7647 21.952C83.3252 14.7232 72.3455 9.10714 58.9898 9.10714ZM19.5369 15.2924C29.1851 6.98214 42.4384 0 58.9898 0C75.5411 0 88.7945 6.98214 98.4426 15.2924C108.029 23.5458 114.441 33.3929 117.493 40.1663C118.169 41.6652 118.169 43.3348 117.493 44.8337C114.441 51.6071 108.029 61.4732 98.4426 69.7076C88.7945 78.0179 75.5411 85 58.9898 85C42.4384 85 29.1851 78.0179 19.5369 69.7076C9.95027 61.4732 3.53867 51.6071 0.506987 44.8337C-0.168996 43.3348 -0.168996 41.6652 0.506987 40.1663C3.53867 33.3929 9.95027 23.5268 19.5369 15.2924Z"
                
            />
            <circle cx="58.5" cy="42.5" r="19.5" />
        </svg>
    );
};

export default ViewIcon;
