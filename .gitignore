# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
/data
# dependencies
node_modules/
.pnp
.pnp.js
.yarn/install-state.gz
*.env
# testing
coverage
ai-python/localstack
# next.js
.next/
out/

# production
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Sentry Config File
.env.sentry-build-plugin

# Continue.Dev
*.continue
*.vscode
# Continue.Dev
*.continue
*.vscode

# Node JS
.prettierrc.json
nodejs/storage
.env
uploads/*
dump.rdb
newrelic_agent.log

