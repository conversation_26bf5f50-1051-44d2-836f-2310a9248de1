import React from 'react';

const AddModal = ({height, width, className, fill}:any) => {
  return (
    <svg className={className}  width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4.99632 8.99992C4.99632 5.14093 8.1359 1.99985 11.9964 1.99985C12.4991 2.00076 13.0008 2.04351 13.4964 2.12765V0.112203C13.0041 0.045001 12.5061 0 11.9964 0C7.02347 0 2.99587 4.0294 2.99587 8.99992L-0.00390625 14.5V15.0001C-0.00390625 15.2755 0.216599 15.544 0.486306 15.5977L2.49606 16L2.84346 19.0136C2.96947 20.1056 3.97299 21.0002 5.07252 21.0002H7.99609L8.99632 24H11.1039L9.75294 19.9484C9.57923 19.4267 8.98612 18.9998 8.4365 18.9998H5.07222C4.98942 18.9998 4.84001 18.8663 4.82981 18.7829L4.3153 14.3242L2.55996 13.9734L4.99572 9.50964V8.99992H4.99632Z" fill={fill}/>
        <path d="M22.4966 7.5002C23.3251 7.5002 23.9967 6.82861 23.9967 6.00016C23.9967 5.17171 23.3251 4.50012 22.4966 4.50012C21.6682 4.50012 20.9966 5.17171 20.9966 6.00016C20.9966 6.82861 21.6682 7.5002 22.4966 7.5002Z" fill={fill}/>
        <path d="M22.4966 3.00007C23.3251 3.00007 23.9967 2.32849 23.9967 1.50004C23.9967 0.67159 23.3251 0 22.4966 0C21.6682 0 20.9966 0.67159 20.9966 1.50004C20.9966 2.32849 21.6682 3.00007 22.4966 3.00007Z" fill={fill}/>
        <path d="M16.4966 3.00007C17.3251 3.00007 17.9967 2.32849 17.9967 1.50004C17.9967 0.67159 17.3251 0 16.4966 0C15.6682 0 14.9966 0.67159 14.9966 1.50004C14.9966 2.32849 15.6682 3.00007 16.4966 3.00007Z" fill={fill}/>
        <path d="M22.4966 17.9998C23.3251 17.9998 23.9967 17.3282 23.9967 16.4998C23.9967 15.6713 23.3251 14.9998 22.4966 14.9998C21.6682 14.9998 20.9966 15.6713 20.9966 16.4998C20.9966 17.3282 21.6682 17.9998 22.4966 17.9998Z" fill={fill}/>
        <path d="M12.9967 14.2501H19.2465L23.2468 10.2501V6.00014H21.7468V9.62904L18.6255 12.75H12.9967V14.2501ZM15.7466 1.74994V6.74986H10.9966V8.2499H17.2466V1.74994H15.7466Z" fill={fill}/>
        <path d="M7.99609 11.25H18.004L20.2466 9.00771V4.8103L23.0259 2.03043L21.9653 0.969604L18.7466 4.18929V8.38669L17.383 9.74992H7.99609V11.25ZM12.0549 15.7498H22.4967V17.2498H11.4339L7.99609 13.8105L9.05662 12.75L12.0549 15.7498Z" fill={fill}/>
    </svg>
  );
}

export default AddModal;