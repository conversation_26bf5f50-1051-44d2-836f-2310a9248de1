# ---------------------------------------------
# NEXT WEAM FRONTEND Local Environment Configuration
# ---------------------------------------------
NEXT_PUBLIC_SERVER_NODE_API_URL=http://node_app:4050/napi
NEXT_PUBLIC_PYTHON_API_URL=http://localhost:9089/pyapi
NEXT_PUBLIC_DOMAIN_URL=http://localhost:3000
NEXT_PUBLIC_API_PREFIX=/api
# Set the environment mode for the build process.
# Use "development" to run the app in development mode (e.g., with hot reload, debug tools).
# Use "production" to build the app for production (optimized, minified, no dev tools).
NEXT_PUBLIC_APP_ENVIRONMENT=production
NEXT_PUBLIC_COMMON_NODE_API_URL=http://localhost:4050/napi

# ---------------------------------------------
# Authentication Configuration
# ---------------------------------------------
NEXT_PUBLIC_COOKIE_NAME=cookie
NEXT_PUBLIC_COOKIE_PASSWORD=82f80fc227d5ee18452b985d94a2f538d3fb1209
NEXT_PUBLIC_AWS_S3_URL=http://minio:9000/weam-frontend-media
NEXT_PUBLIC_HTTPS_PROTOCOL=http
NEXT_PUBLIC_IMAGE_DOMAIN=minio

# ---------------------------------------------
# Socket Configuration
# ---------------------------------------------

NEXT_PUBLIC_SOCKET_CONNECTION_URL=http://localhost:4050
NEXT_PUBLIC_SECURITY_KEY=7c50c824e69218ad5a2f5342
NEXT_PUBLIC_MESSAGE_LIMIT=100
NEXT_PUBLIC_FREE_TRIAL_DAYS=30
NEXT_PUBLIC_OPENAI_PLATFORM_URL=https://platform.openai.com/settings/organization/billing/overview
NEXT_PUBLIC_FRESHDESK_SUPPORT_URL=https://weamai.freshdesk.com/support/tickets/new?ticket_form=report_an_issue
CSRF_TOKEN_SECRET=c063070bebddb878238a298b4d749530

# ---------------------------------------------
# NODE JS
# ---------------------------------------------
SERVER_PORT=4050
API_PREFIX=v1
LOCAL_LOG=false


# ---------------------------------------------
# LLM MODELS API URL 
# ---------------------------------------------

#LLM Model Api URL
FRONT_URL=http://localhost:3000
OPEN_AI_MODAL=https://api.openai.com/v1/models
OPEN_AI_API_URL=https://api.openai.com
GEMINI_API_URL=https://generativelanguage.googleapis.com
ANTHROPIC_AI_API_URL=https://api.anthropic.com/v1
PYTHON_API_URL=http://gateway_service:9089/pyapi
PERPLEXITY_API_URL=https://api.perplexity.ai
OPEN_ROUTER_API_URL=https://openrouter.ai/api


# ---------------------------------------------
# JWT AUTH URL
# ---------------------------------------------


#JWT auth

JWT_SECRET=bc4572d58ec33111eb2b44111617ab8c8503a071
JWT_REFRESH_SECRET=dedc07395ca0e92d17fc8cb3474f892f78e453ff08ca5578d32685429bb7742734e24535
JWT_ACCESS_EXPIRE=24h
JWT_REFRESH_EXPIRE=30d
QR_NAME=weam
SECURITY_KEY=7c50c824e69218ad5a2f5342
JWT_ALGORITHM=HS256


# ---------------------------------------------
#  REDIS CONFIGURATIONS
# ---------------------------------------------

#redis
REDIS_HOST=redis
REDIS_PORT=6379


# ---------------------------------------------
# WEAM Enterprise Local Environment Configuration
# ---------------------------------------------
# Environment type (options: enterprise, local, dev, prod)
WEAM_ENVIRONMENT=enterprise
# Name of the stack (used for AWS CloudFormation or similar tooling)
STACK_NAME=EnterpriseStack
#Root ENV
ENV_FILE=".env"


# --------------------------------------
# Application Gateway & Environment URLs
# --------------------------------------
# Port number used by the FastAPI application gateway
GATEWAY_PORT=9089
# Base URL for the environment (frontend or external service)
ENVIRONMENT_URL=http://localhost:3000


# --------------------------------
# Celery Task Queue Configuration
# --------------------------------

# Redis URL used by Celery as the broker for task distribution
CELERY_BROKEN_URL=redis://redis:6379/7
# Redis URL used by Celery to store task results
CELERY_RESULT_BACKEND=redis://redis:6379/7
# Automatically delete tasks from the backend when completed successfully (true/false)
DELETE_TASK_ON_SUCCESS=true


# -------------------------------
# AWS S3 & CDN Configuration
# -------------------------------

AWS_S3_API_VERSION=2012-10-17
# Type of storage bucket used (e.g., AWS_S3, MINIO, LOCALSTACK)
# BUCKET_TYPE=AWS_S3

# AWS credentials for accessing S3
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=

# AWS region where your S3 buckets are hosted
# AWS_REGION=us-east-1

# S3 bucket name used for storing frontend media files (e.g., images, videos)
# AWS_S3_BUCKET=sample-frontend-media-bucket
# S3 bucket name used for profiler-related data
# PROFILER_S3_BUCKET=sample-profiler
# S3 bucket for backing up vector data (e.g., from Pinecone or Qdrant)
# AWS_VECTORS_BACKUP="sample-vectors-backup-bucket"

# Base S3 URL pattern (replace <bucket_name> dynamically in code)
# AWS_S3_URL=https://<bucket_name>.s3.amazonaws.com

# CDN URL used to serve static files from S3
# AWS_CDN_URL=https://cdn.example.com  
# Regex pattern to match specific S3 file keys (e.g., domain-specific files)
# DOMAIN SAMPLE: example.com
# AWS_REGEX_FILE_PATTERN=.*example\.com.*



# ----------------------------
# MinIO Local Storage Settings
# ----------------------------
# Bucket type (optional override, e.g., MINIO instead of AWS_S3)

BUCKET_TYPE=MINIO
# Port used by the MinIO S3-compatible API
MINIO_PORT=9000
# Port used for accessing the MinIO Web Dashboard
MINIO_DASHBOARD_PORT=9001
# MinIO access credentials (used in place of AWS credentials)
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin123
# MinIO bucket name for storing frontend media files
AWS_BUCKET=weam-frontend-media
# Base endpoint URL for MinIO access
MINIO_ENDPOINT=http://minio:9000
# Regex pattern for matching URLs pointing to MinIO buckets
MINIO_REGEX_FILE_PATTERN="^https?://(?:localhost|minio):9000/"
# S3 bucket name used for profiler-related data
PROFILER_S3_BUCKET=sample-profiler
# S3 bucket for backing up vector data (e.g., from Pinecone or Qdrant)
AWS_VECTORS_BACKUP="sample-vectors-backup-bucket"
INTERNAL_ENDPOINT=http://localhost:9000

# -------------------------------------
# Email Provider Configuration (SES/SMTP)
# -------------------------------------
# SES
# Type of email service used (options: SES, SMTP)
# EMAIL_PROVIDER=SES
# <AUTHOR> <EMAIL>"


EMAIL_PROVIDER=SMTP
# SMTP authentication credentials
SMTP_USER=
SMTP_PASSWORD=
# Email address used as the sender in outgoing emails
SENDER_EMAIL=
# SMTP server configuration
SMTP_PORT=
SMTP_SERVER=
SENDER_EMAIL="Weam <<EMAIL>>"
SUPPORT_EMAIL=<EMAIL>
API_RATE_LIMIT=10
SEED=1
TZ=Asia/Kolkata
FRESHDESK_SUPPORT_URL=https://weamai.freshdesk.com/support/tickets/new?ticket_form=report_an_issue
FREE_TIER_CREDIT=1000


# -----------------------------
# MongoDB Development Settings
# -----------------------------
## Cloud MongoDB (MongoDB Atlas or similar)
# Full MongoDB connection URI (replace placeholders with actual credentials and host)
#  MONOGODB_URI=mongodb+srv://username:<EMAIL>

# To see db in docker local commands
# docker exec -it weamai-mongo-1 sh
# mongosh --username admin --password password --authenticationDatabase admin

## Local MongoDB Setup
MONOGODB_URI=***********************************************************************************************
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password
# Name of the MongoDB database used by the application
DB_DATABASE=test
# Default MongoDB port (typically 27017)
MONGO_PORT=27017


# ---------------------------
# Qdrant Vector DB Configuration
# ---------------------------
## Cloud Qdrant (Uncomment to use managed Qdrant service)
# QDRANT_URL=https://<your-qdrant-instance>.us-west-1-0.aws.cloud.qdrant.io:6333
# QDRANT_API_KEY=<your_qdrant_api_key>
## Local Qdrant Setup
# URL of the primary local Qdrant container
LOCAL_QDRANT_URL=http://qdrant_primary:6333
# Port used for the Qdrant web interface or dashboard
QDRANT_DASHBOARD_PORT=6333
# Port exposed for internal API or external access (if different from dashboard)
QDRANT_NODE_PORT=6335



# --------------------------
# WDB (Web Debugger) Settings
# --------------------------
# Hostname or container name where the WDB socket server is running
WDB_SOCKET_SERVER=wdb
# Prevents WDB from automatically opening a browser window (1 = true)
WDB_NO_BROWSER_AUTO_OPEN=1
# Port on which the WDB debugger server listens
WDB_PORT=1984

## PROAGENT 

# ----------------------------
# QA PRO Agent Authentication
# ----------------------------
GOOGLE_PAGE_SPEED=
# ----------------------------

# ----------------------------
# SEO Agent Authentication
# ----------------------------
# Username or user ID used to authenticate with the SEO agent service
# DATA FOR SEO Credential reference https://dataforseo.com/
SEO_USER_ID=
# Password or API key used for authentication (keep this secure)
SEO_PASSWORD=
#mcp
MCP_PORT=8000
MCP_URL=http://mcp:8000/sse

NEXT_PUBLIC_SLACK_CLIENT_ID=
NEXT_PUBLIC_SLACK_CLIENT_SECRET=

NEXT_PUBLIC_GITHUB_CLIENT_ID=
NEXT_PUBLIC_GITHUB_CLIENT_SECRET=

NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID=
NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_SECRET=