import React from 'react';

const ExportIcon = ({ width, height, className }: any) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 21 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.69234 13.7306H19.3846C19.5989 13.7306 19.8043 13.6455 19.9558 13.4941C20.1072 13.3426 20.1923 13.1371 20.1923 12.9229C20.1923 12.7087 20.1072 12.5033 19.9558 12.3518C19.8043 12.2003 19.5989 12.1152 19.3846 12.1152H9.69234C9.47812 12.1152 9.27268 12.2003 9.12121 12.3518C8.96974 12.5033 8.88464 12.7087 8.88464 12.9229C8.88464 13.1371 8.96974 13.3426 9.12121 13.4941C9.27268 13.6455 9.47812 13.7306 9.69234 13.7306Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16.3905 10.2633L19.0502 12.923L16.3905 15.5827C16.2434 15.7351 16.1619 15.9391 16.1638 16.1509C16.1656 16.3626 16.2506 16.5652 16.4003 16.715C16.5501 16.8647 16.7527 16.9497 16.9644 16.9515C17.1762 16.9534 17.3802 16.8719 17.5326 16.7248L20.7633 13.494C20.8384 13.4191 20.898 13.3301 20.9386 13.2321C20.9792 13.1341 21.0001 13.0291 21.0001 12.923C21.0001 12.8169 20.9792 12.7119 20.9386 12.6139C20.898 12.5159 20.8384 12.4269 20.7633 12.352L17.5326 9.12119C17.3802 8.97407 17.1762 8.89266 16.9644 8.8945C16.7527 8.89634 16.5501 8.98128 16.4003 9.13103C16.2506 9.28079 16.1656 9.48337 16.1638 9.69514C16.1619 9.90691 16.2434 10.1109 16.3905 10.2633ZM8.0769 1.61531V4.84608C8.0769 5.48872 8.33219 6.10504 8.78661 6.55945C9.24102 7.01387 9.85734 7.26916 10.5 7.26916H13.7308C13.945 7.26916 14.1504 7.18406 14.3019 7.03259C14.4533 6.88112 14.5384 6.67568 14.5384 6.46146C14.5384 6.24725 14.4533 6.04181 14.3019 5.89034C14.1504 5.73887 13.945 5.65377 13.7308 5.65377H10.5C10.2858 5.65377 10.0803 5.56868 9.92886 5.4172C9.77738 5.26573 9.69229 5.06029 9.69229 4.84608V1.61531C9.69229 1.4011 9.60719 1.19566 9.45572 1.04418C9.30425 0.892713 9.09881 0.807617 8.8846 0.807617C8.67038 0.807617 8.46494 0.892713 8.31347 1.04418C8.162 1.19566 8.0769 1.4011 8.0769 1.61531Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.3462 9.69231V6.46154C15.3463 6.35546 15.3254 6.2504 15.2848 6.15241C15.2442 6.05441 15.1846 5.9654 15.1095 5.8905L9.45565 0.236654C9.38075 0.16154 9.29174 0.101966 9.19375 0.0613536C9.09575 0.0207411 8.99069 -0.000109152 8.88462 4.29706e-07H2.42308C1.78015 4.29706e-07 1.16388 0.255231 0.709962 0.709962C0.4848 0.934807 0.306194 1.20185 0.184369 1.49581C0.0625438 1.78977 -0.00010788 2.10487 1.39442e-07 2.42308V18.5769C1.39442e-07 19.2198 0.255231 19.8361 0.709962 20.29C0.934807 20.5152 1.20185 20.6938 1.49581 20.8156C1.78977 20.9375 2.10487 21.0001 2.42308 21H12.9231C13.566 21 14.1823 20.7448 14.6362 20.29C14.8614 20.0652 15.04 19.7981 15.1618 19.5042C15.2836 19.2102 15.3463 18.8951 15.3462 18.5769V16.1538C15.3462 15.9396 15.2611 15.7342 15.1096 15.5827C14.9581 15.4312 14.7527 15.3462 14.5385 15.3462C14.3242 15.3462 14.1188 15.4312 13.9673 15.5827C13.8159 15.7342 13.7308 15.9396 13.7308 16.1538V18.5769C13.7311 18.6831 13.7104 18.7883 13.6699 18.8864C13.6295 18.9845 13.57 19.0737 13.4949 19.1488C13.4199 19.2238 13.3307 19.2833 13.2325 19.3238C13.1344 19.3643 13.0292 19.3849 12.9231 19.3846H2.42308C2.31692 19.3849 2.21175 19.3643 2.11361 19.3238C2.01547 19.2833 1.9263 19.2238 1.85124 19.1488C1.77617 19.0737 1.71669 18.9845 1.67622 18.8864C1.63574 18.7883 1.61506 18.6831 1.61538 18.5769V2.42308C1.61506 2.31692 1.63574 2.21175 1.67622 2.11361C1.71669 2.01547 1.77617 1.9263 1.85124 1.85124C1.9263 1.77617 2.01547 1.71669 2.11361 1.67622C2.21175 1.63574 2.31692 1.61507 2.42308 1.61538H8.55023L13.7308 6.79592V9.69231C13.7308 9.90652 13.8159 10.112 13.9673 10.2634C14.1188 10.4149 14.3242 10.5 14.5385 10.5C14.7527 10.5 14.9581 10.4149 15.1096 10.2634C15.2611 10.112 15.3462 9.90652 15.3462 9.69231Z"
            />
        </svg>
    );
};

export default ExportIcon;
