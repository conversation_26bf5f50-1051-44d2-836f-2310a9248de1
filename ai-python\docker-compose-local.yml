services:

  redis:
    image: "redis/redis-stack:latest" # Image name local developement with dashboard
    container_name: redis_container # Container name
    volumes:
      - redis_data:/data
    networks:
      - ai_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 3s
      timeout: 5s
      retries: 5

  pybase_docker:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: BaseDockerfile
    image: pybase_image


  celery_service:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: DockerfileRed
    image: "celery_app" # Custom image name
    container_name: celery_service # Container name
    depends_on:
      - pybase_docker
      - redis
    networks:
      - ai_network
    entrypoint: /usr/local/bin/celery_entrypoint.sh
    volumes:
      - .:/app


  extraction_worker:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: src/celery_worker_hub/extraction/Dockerfile
    image: "extraction_worker" # Custom image name
    container_name: extraction_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
    networks:
      - ai_network
    entrypoint: /usr/local/bin/extraction_worker.sh
    volumes:
      - .:/app

  scrapper_worker:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: src/celery_worker_hub/web_scraper/Dockerfile
    image: "scrapper_worker" # Custom image name
    container_name: scrapper_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
    networks:
      - ai_network
    entrypoint: /usr/local/bin/scraper_worker.sh
    volumes:
      - .:/app

  import_worker:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: src/celery_worker_hub/import_worker/Dockerfile
    image: "import_worker" # Custom image name
    container_name: import_worker_svc # Container name
    depends_on:
      - pybase_docker
      - redis
    networks:
      - ai_network
    entrypoint: /usr/local/bin/import_chat_worker.sh
    volumes:
      - .:/app
  

  
  gateway:
    env_file: ${ENV_FILE}
    build:
      context: .
      dockerfile: Dockerfile
    image: "gateway_app" # Custom image name
    container_name: gateway_service # Container name
    ports:
      - "${GATEWAY_PORT}:${GATEWAY_PORT}"
    depends_on:
      - pybase_docker
      - redis
      - celery_service
      - extraction_worker
      - qdrant_primary
    networks:
      - ai_network
    command: uvicorn src.gateway.web:app --host 0.0.0.0 --port ${GATEWAY_PORT} --reload --workers 2
    volumes:
      - .:/app
    

  wdb:
    image: kozea/wdb:3.3.0
    container_name: web-wdb
    networks:
      - ai_network
    ports:
      - ${WDB_PORT}:${WDB_PORT}
    depends_on:
      - gateway
  
  qdrant_primary:
    image: "qdrant/qdrant:latest"
    container_name: qdrant_primary_container  # 👈 Add this to both nodes
    ports:
     
      - "${QDRANT_NODE_PORT}:${QDRANT_NODE_PORT}"  # 👈 Add this to both nodes
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      RUST_LOG: "trace"
    command: ["./qdrant", "--uri", "http://qdrant_primary:${QDRANT_NODE_PORT}","--config-path", "/qdrant/config/config.yml"]
    volumes:
      - qdrant_primary_data:/qdrant/storage
      - qdrant_primary_snapshots:/qdrant/snapshots
      - ./config_files/qdrant:/qdrant/config:ro

    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - ai_network

  qdrant_secondary:
    image: "qdrant/qdrant:latest"
    environment:
      QDRANT__CLUSTER__ENABLED: "true"
      RUST_LOG: "trace"
    command: ["./qdrant", "--bootstrap", "http://qdrant_primary:${QDRANT_NODE_PORT}", "--config-path", "/qdrant/config/config.yml"]
    volumes:
      - qdrant_secondary_data:/qdrant/storage
      - qdrant_secondary_snapshots:/qdrant/snapshots
      - ./config_files/qdrant:/qdrant/config:ro
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 60s  # Increased interval
      timeout: 20s  # Increased timeout
      retries: 10   # Increased retries
    networks:
      - ai_network
  mongodb:
    image: mongo:latest
    ports:
      - "${MONGO_PORT}:${MONGO_PORT}"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
    volumes:
      - mongo-data:/data/db
    networks:
      - ai_network

  
  minio:
    image: minio/minio:latest
    container_name: minio
    ports:
      - "${MINIO_PORT}:${MINIO_PORT}"     # S3 API port
      - "${MINIO_DASHBOARD_PORT}:${MINIO_DASHBOARD_PORT}"     # Web UI port
    volumes:
      - minio_data:/data
    environment:
      - MINIO_ROOT_USER=${AWS_ACCESS_KEY_ID}
      - MINIO_ROOT_PASSWORD=${AWS_SECRET_ACCESS_KEY}
    command: server /data --console-address ":${MINIO_DASHBOARD_PORT}"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${MINIO_PORT}/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - ai_network
  
  minio-init:
    image: minio/mc
    env_file: ${ENV_FILE}
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: ["/bin/sh", "/minio.sh"]
    volumes:
      - ./minio.sh:/minio.sh:ro
    networks:
      - ai_network



    



  
volumes:
  mongo-data:
  redis_data:
  minio_data:
  app:
  db:
  localstack:
  qdrant_primary_data:
  qdrant_primary_snapshots:
  qdrant_secondary_data:
  qdrant_secondary_snapshots:

networks:
  ai_network:
    name: ai_network
    driver: bridge