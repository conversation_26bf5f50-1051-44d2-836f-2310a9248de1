import React from 'react';

const Moveto = ({ height, width, className }) => {
    return (
        <svg
            className={className}
            width={width}
            height={height}
            viewBox="0 0 224 193"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M123.825 31.2558C119.564 31.2558 115.574 29.1721 113.136 25.6819L103.79 12.2731C101.151 8.48504 97.6356 5.39081 93.5432 3.25389C89.4508 1.11698 84.9025 0.000662275 80.2858 0H28.6512C21.0524 0 13.7649 3.0186 8.39173 8.39173C3.0186 13.7649 0 21.0524 0 28.6512V164.093C0 171.688 3.0214 178.981 8.38698 184.357C13.7653 189.725 21.0526 192.741 28.6512 192.744H195.349C202.944 192.744 210.237 189.723 215.613 184.357C220.981 178.979 223.997 171.692 224 164.093V59.907C224 52.3118 220.979 45.0188 215.613 39.6428C210.235 34.2751 202.947 31.259 195.349 31.2558H123.825ZM123.825 46.8837H195.349C198.801 46.8892 202.11 48.2631 204.552 50.7042C206.993 53.1454 208.367 56.4547 208.372 59.907V164.093C208.367 167.545 206.993 170.855 204.552 173.296C202.11 175.737 198.801 177.111 195.349 177.116H28.6512C25.1989 177.111 21.8896 175.737 19.4484 173.296C17.0073 170.855 15.6334 167.545 15.6279 164.093V28.6512C15.6279 21.4623 21.4623 15.6279 28.6512 15.6279H80.2858C84.547 15.6279 88.5373 17.7116 90.9753 21.2019L100.321 34.6106C102.959 38.3993 106.475 41.494 110.567 43.631C114.66 45.768 119.208 46.8839 123.825 46.8837Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M146.538 105H83.4615C80.9522 105 78.5456 105.948 76.7712 107.636C74.9968 109.324 74 111.613 74 114C74 116.387 74.9968 118.676 76.7712 120.364C78.5456 122.052 80.9522 123 83.4615 123H146.538C149.048 123 151.454 122.052 153.229 120.364C155.003 118.676 156 116.387 156 114C156 111.613 155.003 109.324 153.229 107.636C151.454 105.948 149.048 105 146.538 105Z"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M114.76 95.2877L133.232 114L114.76 132.712C113.885 133.598 113.191 134.65 112.717 135.808C112.244 136.965 112 138.206 112 139.459C112 140.712 112.244 141.953 112.717 143.11C113.191 144.268 113.885 145.32 114.76 146.205C115.636 147.091 116.675 147.794 117.818 148.274C118.961 148.753 120.187 149 121.425 149C122.662 149 123.888 148.753 125.031 148.274C126.175 147.794 127.214 147.091 128.089 146.205L153.237 120.747C154.113 119.861 154.808 118.809 155.282 117.652C155.756 116.494 156 115.253 156 114C156 112.747 155.756 111.506 155.282 110.348C154.808 109.191 154.113 108.139 153.237 107.253L128.089 81.7945C127.214 80.9086 126.175 80.2058 125.031 79.7263C123.888 79.2468 122.662 79 121.425 79C120.187 79 118.961 79.2468 117.818 79.7263C116.675 80.2058 115.636 80.9086 114.76 81.7945C113.885 82.6805 113.191 83.7323 112.717 84.8899C112.244 86.0475 112 87.2882 112 88.5411C112 89.7941 112.244 91.0348 112.717 92.1924C113.191 93.35 113.885 94.4018 114.76 95.2877Z"
            />
        </svg>
    );
};

export default Moveto;
