
import React from 'react';

const CheckIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 28 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M27.4861 0.624152C27.6363 0.755623 27.7588 0.915635 27.8466 1.09489C27.9343 1.27414 27.9856 1.46905 27.9974 1.66829C28.0092 1.86752 27.9812 2.06712 27.9151 2.25545C27.8491 2.44379 27.7462 2.61712 27.6126 2.76535L11.781 20.3361C11.6467 20.4849 11.484 20.6054 11.3026 20.6906C11.1211 20.7757 10.9245 20.8237 10.7242 20.8319C10.5239 20.84 10.3241 20.8081 10.1363 20.7379C9.94852 20.6678 9.77664 20.5609 9.63073 20.4235L0.469646 11.8C0.194811 11.5375 0.0284067 11.1816 0.0033148 10.8024C-0.0217771 10.4232 0.0962693 10.0485 0.334127 9.75211C0.462517 9.59145 0.622217 9.45855 0.803534 9.3615C0.98485 9.26444 1.184 9.20524 1.38889 9.1875C1.59379 9.16975 1.80015 9.19383 1.99545 9.25828C2.19075 9.32272 2.37092 9.42618 2.52502 9.56238L9.63223 15.9559C9.92899 16.223 10.3197 16.3613 10.7184 16.3404C11.1171 16.3195 11.4912 16.1411 11.7584 15.8444L25.375 0.746119C25.6401 0.45225 26.0102 0.274541 26.4052 0.251434C26.8003 0.228328 27.1886 0.36168 27.4861 0.622646V0.624152Z"/>
    </svg>
  );
}

export default CheckIcon;