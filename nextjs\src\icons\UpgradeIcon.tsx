import React from 'react';

const UpgradeIcon = ({height, width, className}:any) => {
  return (
    <svg className={className} width={width} height={height} viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.5997 10.3104H19.6924C19.5933 10.3104 19.5054 10.2477 19.4737 10.1557C19.3436 9.76416 19.183 9.37847 18.9972 9.01036C18.9526 8.9201 18.972 8.81283 19.0488 8.74074L19.6847 8.10066C20.2404 7.5444 20.2404 6.63879 19.6847 6.08253L19.0095 5.40729C18.8987 5.30002 18.7522 5.24082 18.598 5.24082C18.4357 5.24082 18.2844 5.3053 18.1719 5.42194C17.945 5.65699 17.9509 6.03271 18.1842 6.25779L18.8465 6.92015C18.9409 7.01452 18.9403 7.16868 18.8454 7.26305L18.2129 7.90019C17.7739 8.33101 17.6631 8.99044 17.938 9.542C18.0992 9.86087 18.2375 10.1944 18.3501 10.5344C18.5453 11.1094 19.0857 11.4956 19.6953 11.4956H20.5997C20.7328 11.4956 20.8406 11.6041 20.8406 11.7366V14.2371C20.8406 14.3701 20.7322 14.478 20.5997 14.478H19.6918C19.0845 14.478 18.5453 14.8643 18.3501 15.4405C18.237 15.781 18.0974 16.1163 17.9345 16.4381C17.6643 16.9856 17.7745 17.6409 18.2082 18.0682L18.8471 18.7118C18.9409 18.8061 18.9409 18.9591 18.8471 19.0535L17.0828 20.8178C17.0359 20.8624 16.9744 20.887 16.9093 20.887C16.8443 20.887 16.7821 20.8624 16.737 20.819L16.0998 20.1865C15.8355 19.9169 15.4674 19.7622 15.0893 19.7622C14.8719 19.7622 14.6538 19.8137 14.4574 19.911C14.1386 20.0722 13.8056 20.2106 13.4657 20.3231C12.8889 20.5189 12.502 21.0599 12.5044 21.6683V22.5728C12.5044 22.7058 12.3959 22.8137 12.2635 22.8137H9.76295C9.62989 22.8137 9.52204 22.7052 9.52204 22.5728V21.6689C9.5238 21.0599 9.13694 20.5195 8.55958 20.3231C8.21844 20.21 7.88316 20.0705 7.56195 19.9069C7.36852 19.8114 7.15281 19.761 6.93711 19.761C6.55552 19.761 6.19856 19.9105 5.93186 20.1807L5.29119 20.8166C5.2443 20.8618 5.18217 20.8864 5.11711 20.8864C5.05205 20.8864 4.9905 20.8618 4.94654 20.8202L3.17929 19.0529C3.08492 18.9585 3.0855 18.8044 3.18046 18.71L3.81292 18.0729C4.25195 17.642 4.36273 16.982 4.08782 16.4311C3.92663 16.1122 3.7883 15.7793 3.67576 15.4387C3.48057 14.8637 2.94014 14.4774 2.33054 14.4774H1.42611C1.29305 14.4774 1.1852 14.369 1.1852 14.2365V11.736C1.1852 11.6029 1.29364 11.4951 1.42611 11.4951H2.33347C2.94073 11.4951 3.47999 11.1088 3.67517 10.5326C3.7883 10.192 3.92781 9.85677 4.09134 9.53497C4.36156 8.98751 4.25136 8.33219 3.81761 7.90547L3.1787 7.26187C3.08492 7.1675 3.08492 7.01452 3.1787 6.92015L3.85395 6.2449C4.08079 6.00985 4.07434 5.63413 3.8393 5.4067C3.72793 5.29944 3.58198 5.24023 3.42782 5.24023C3.26545 5.24023 3.11423 5.30412 3.00344 5.41901L2.34051 6.08136C1.78542 6.63703 1.78483 7.54205 2.33933 8.09831L2.9841 8.74718C3.05444 8.81459 3.07378 8.91775 3.03216 9.00333C2.8446 9.37378 2.68282 9.76123 2.55269 10.1534C2.52045 10.2471 2.43136 10.3099 2.33054 10.3099H1.42611C0.640664 10.3105 0.000586152 10.9505 0 11.736V14.2371C0.000586152 15.0225 0.640664 15.6626 1.42611 15.6632H2.33347C2.43253 15.6632 2.52045 15.7259 2.55211 15.8179C2.68223 16.2101 2.84284 16.5958 3.02865 16.9633C3.0732 17.053 3.05385 17.1602 2.97707 17.2329L2.34051 17.873C1.78483 18.4292 1.78483 19.3348 2.34051 19.8911L4.11068 21.6613C4.38325 21.921 4.74021 22.064 5.11652 22.064C5.49225 22.064 5.8498 21.921 6.12412 21.6595L6.7724 21.0153C6.8146 20.9714 6.87322 20.9462 6.93418 20.9462C6.96818 20.9462 7.00041 20.9538 7.02855 20.9673C7.399 21.1554 7.78644 21.3166 8.17858 21.4468C8.27295 21.479 8.33567 21.5681 8.33508 21.6689V22.5733C8.33567 23.3588 8.97575 23.9989 9.76119 23.9994H12.2623C13.0477 23.9989 13.6878 23.3588 13.6884 22.5733V21.6677C13.6878 21.5681 13.7505 21.4796 13.8432 21.4473C14.2353 21.3172 14.621 21.1566 14.9891 20.9702C15.0207 20.9544 15.0547 20.9462 15.0899 20.9462C15.152 20.9462 15.2124 20.9726 15.2587 21.0218L15.9017 21.6613C16.1743 21.921 16.5312 22.064 16.9076 22.064C17.2839 22.064 17.6414 21.9204 17.9163 21.6584L19.6836 19.8917C20.2387 19.336 20.2392 18.431 19.6847 17.8747L19.04 17.2259C18.9696 17.1585 18.9503 17.0553 18.9919 16.9697C19.1795 16.5993 19.3413 16.2124 19.4714 15.8197C19.5036 15.7259 19.5927 15.6632 19.6935 15.6632H20.598C21.3834 15.6626 22.0235 15.0225 22.0241 14.2371V11.736C22.0235 10.9505 21.3834 10.3105 20.598 10.3099L20.5997 10.3104Z"/>
        <path d="M10.9072 18.9973L11.0145 18.9979H11.1265V18.9968C12.9476 18.9639 14.6287 18.1251 15.7524 16.685C16.7395 15.4195 17.175 13.8451 16.9786 12.2519C16.7822 10.6587 15.9763 9.23791 14.7102 8.24965C14.6059 8.16935 14.481 8.12656 14.3491 8.12656C14.1668 8.12656 13.9968 8.20862 13.8837 8.35164C13.7858 8.47591 13.7419 8.63065 13.76 8.78774C13.7782 8.94483 13.8568 9.08551 13.9822 9.18457C14.2929 9.42723 14.5736 9.708 14.8163 10.0187C16.454 12.1159 16.08 15.1551 13.9822 16.7928C13.1258 17.4616 12.0995 17.8151 11.0157 17.8151C9.51747 17.8151 8.12947 17.1387 7.20862 15.9587C5.57091 13.8615 5.94488 10.8223 8.0433 9.18398C8.29652 8.9841 8.34224 8.61307 8.14529 8.35751C8.03216 8.21038 7.86101 8.12598 7.6752 8.12598C7.54331 8.12598 7.41846 8.16877 7.31296 8.24965C5.8423 9.39675 4.99941 11.1247 5 12.9899C5.00117 16.2676 7.64179 18.9411 10.9072 18.9956V18.9973Z"/>
        <path d="M6.89403 6.57897C6.9843 6.57897 7.07456 6.57018 7.16014 6.55377L8.87171 6.24369V12.2465C8.87229 12.9206 9.42093 13.4692 10.095 13.4698H11.9326C12.6067 13.4692 13.1553 12.92 13.1559 12.2459L13.1571 6.24252L14.8616 6.55201C14.9507 6.56959 15.0415 6.57839 15.1318 6.57839C15.7062 6.57839 16.2326 6.21732 16.4436 5.67571C16.6236 5.17807 16.4975 4.61536 16.1224 4.24316L12.4912 0.611943C12.0967 0.217462 11.5721 0 11.0135 0C10.4549 0 9.93088 0.217462 9.53582 0.611943L5.90578 4.24198C5.53005 4.61478 5.40403 5.17865 5.58632 5.68216C5.79382 6.21849 6.3196 6.57897 6.89403 6.57897ZM15.2953 5.25544C15.266 5.31757 15.2039 5.35743 15.1353 5.35743C15.1213 5.35743 15.1084 5.35567 15.0908 5.35215L13.3763 5.04032C13.3042 5.02743 13.2303 5.02039 13.1571 5.02039C12.4824 5.02039 11.9338 5.56903 11.9338 6.24369L11.9326 12.2471L10.0944 12.2459V6.24428C10.0956 5.5702 9.54754 5.02098 8.87347 5.01981C8.79961 5.01981 8.72576 5.02625 8.65366 5.03974L6.93096 5.35333C6.91865 5.35626 6.90517 5.35743 6.89227 5.35743C6.82369 5.35743 6.76156 5.31816 6.73284 5.2572C6.71643 5.20503 6.73049 5.14642 6.76977 5.10773L10.3957 1.48121C10.5633 1.32119 10.7826 1.23326 11.0141 1.23326C11.2456 1.23326 11.4643 1.3206 11.6272 1.47593L15.2584 5.10714C15.2977 5.14642 15.3112 5.20386 15.2953 5.25544Z"/>
    </svg>
  );
}

export default UpgradeIcon;
