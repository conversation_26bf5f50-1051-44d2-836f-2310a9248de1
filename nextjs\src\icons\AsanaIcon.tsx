const AsanaIcon = ({ className }) => {
    return (
        <svg
            className={className}
            width="24"
            height="23"
            viewBox="0 0 24 23"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.9592 0.430842C10.608 0.152923 11.3055 0.00655239 12.0112 0.000215001C12.717 -0.00612239 13.417 0.1277 14.0706 0.393923C14.7243 0.660147 15.3186 1.05347 15.8191 1.55111C16.3196 2.04874 16.7164 2.64077 16.9864 3.29288C17.2563 3.94499 17.3942 4.64421 17.3919 5.35C17.3897 6.05579 17.2473 6.75411 16.9731 7.40447C16.699 8.05483 16.2984 8.6443 15.7947 9.1387C15.291 9.6331 14.6942 10.0226 14.0388 10.2846C12.7385 10.7936 11.2905 10.7722 10.0057 10.2252C8.721 9.67811 7.70208 8.64897 7.16787 7.35884C6.63366 6.0687 6.62678 4.62051 7.14871 3.32535C7.67063 2.0302 8.67972 0.991423 9.9592 0.432175V0.430842ZM11.999 2.02536C11.115 2.02536 10.2673 2.37652 9.64221 3.00158C9.01715 3.62665 8.66599 4.47442 8.66599 5.35839C8.66599 6.24237 9.01715 7.09014 9.64221 7.7152C10.2673 8.34026 11.115 8.69142 11.999 8.69142C12.883 8.69142 13.7308 8.34026 14.3558 7.7152C14.9809 7.09014 15.332 6.24237 15.332 5.35839C15.332 4.47442 14.9809 3.62665 14.3558 3.00158C13.7308 2.37652 12.883 2.02536 11.999 2.02536ZM3.29315 12.4311C3.94121 12.1571 4.63708 12.0141 5.34066 12.0103C6.04424 12.0065 6.74162 12.142 7.39261 12.4089C8.04361 12.6758 8.63535 13.0689 9.13374 13.5655C9.63213 14.0622 10.0273 14.6525 10.2965 15.3026C10.5658 15.9526 10.7037 16.6495 10.7024 17.3531C10.701 18.0567 10.5605 18.753 10.2889 19.4021C10.0172 20.0511 9.61981 20.64 9.11956 21.1347C8.61931 21.6295 8.0261 22.0204 7.37411 22.2848C6.7271 22.5529 6.03363 22.6909 5.3333 22.6909C4.63296 22.691 3.93948 22.5531 3.29244 22.2851C1.98568 21.7439 0.947422 20.7058 0.406076 19.3991C-0.135269 18.0924 -0.135362 16.6242 0.405818 15.3174C0.946999 14.0107 1.98646 12.9724 3.29315 12.4311ZM5.33296 14.0243C4.44881 14.0243 3.60087 14.3755 2.97568 15.0007C2.35049 15.6259 1.99926 16.4738 1.99926 17.358C1.99926 18.2421 2.35049 19.0901 2.97568 19.7152C3.60087 20.3404 4.44881 20.6917 5.33296 20.6917C6.21711 20.6917 7.06505 20.3404 7.69024 19.7152C8.31543 19.0901 8.66666 18.2421 8.66666 17.358C8.66666 16.4738 8.31543 15.6259 7.69024 15.0007C7.06505 14.3755 6.21711 14.0243 5.33296 14.0243ZM18.6651 12.0245C17.2507 12.0245 15.8943 12.5863 14.8942 13.5864C13.8941 14.5865 13.3322 15.9429 13.3322 17.3573C13.3322 18.7717 13.8941 20.1281 14.8942 21.1282C15.8943 22.1283 17.2507 22.6901 18.6651 22.6901C20.0794 22.6901 21.4359 22.1283 22.436 21.1282C23.4361 20.1281 23.9979 18.7717 23.9979 17.3573C23.9979 15.9429 23.4361 14.5865 22.436 13.5864C21.4359 12.5863 20.0794 12.0245 18.6651 12.0245ZM17.3892 14.2776C18.206 13.9392 19.1237 13.9391 19.9406 14.2774C20.345 14.4449 20.7125 14.6904 21.0221 15C21.3317 15.3095 21.5772 15.677 21.7448 16.0814C21.9123 16.4858 21.9986 16.9193 21.9986 17.3571C21.9987 17.7949 21.9125 18.2283 21.745 18.6328C21.5775 19.0373 21.3319 19.4048 21.0224 19.7143C20.7129 20.0239 20.3454 20.2695 19.941 20.437C19.5365 20.6046 19.1031 20.6908 18.6653 20.6908C18.2275 20.6909 17.794 20.6047 17.3896 20.4372C16.5727 20.0989 15.9237 19.45 15.5854 18.6332C15.4178 18.2287 15.3316 17.7953 15.3315 17.3575C15.3315 16.9197 15.4177 16.4862 15.5852 16.0818C15.9235 15.265 16.5724 14.616 17.3892 14.2776Z"
                fill="#FF584A"
            />
        </svg>
    );
};

export default AsanaIcon;
