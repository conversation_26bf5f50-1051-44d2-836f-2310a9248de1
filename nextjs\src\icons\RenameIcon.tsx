import React from 'react';

const RenameIcon = ({height, width, className}:any) => {
  return (
    <div>
      <svg className={className} width={width} height={height} viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.56312 3.62053C0.621302 3.6409 0.699848 3.63217 0.752212 3.55653L1.37767 2.6169C1.3915 2.59731 1.40979 2.58129 1.43104 2.57016C1.45228 2.55903 1.47587 2.55312 1.49985 2.5529H5.07512C5.15367 2.5529 5.22058 2.61981 5.22058 2.69835V10.6634C5.22058 10.7274 5.17694 10.7856 5.11585 10.8031L4.25476 11.0707C4.0453 11.1347 3.90858 11.3238 3.90858 11.5362C3.90858 11.7078 4.04821 11.8445 4.21985 11.8445H8.1704C8.64662 11.8152 8.51774 11.1621 8.13549 11.0707L7.27149 10.8031C7.24201 10.7934 7.21632 10.7748 7.19804 10.7497C7.17977 10.7246 7.16984 10.6945 7.16967 10.6634V2.69835C7.16967 2.61981 7.23658 2.5529 7.31512 2.5529H10.8904C10.9399 2.5529 10.9864 2.57908 11.0126 2.6169L11.638 3.55653C11.6875 3.62926 11.7689 3.6409 11.8271 3.62053C11.8533 3.61472 11.9435 3.5769 11.9435 3.46635V1.58999C11.9435 1.10417 11.5449 0.711441 11.0562 0.711441H1.33403C0.842393 0.711441 0.443848 1.10417 0.443848 1.58999V3.46635C0.443848 3.5769 0.534029 3.61472 0.56312 3.62053Z" />
        <path d="M11.9743 10.1282H10.5227C10.4069 10.1282 10.2959 10.1742 10.2141 10.256C10.1323 10.3378 10.0863 10.4488 10.0863 10.5646C10.0863 10.6803 10.1323 10.7913 10.2141 10.8731C10.2959 10.9549 10.4069 11.0009 10.5227 11.0009H11.9743C12.5291 11.0009 12.9806 11.4454 12.9806 11.9923C12.9806 12.5392 12.5291 12.9843 11.974 12.9843H1.86578C-0.619455 13.0766 -0.6244 16.6169 1.86578 16.7115H3.30957C3.4253 16.7115 3.53629 16.6655 3.61812 16.5837C3.69996 16.5018 3.74593 16.3908 3.74593 16.2751C3.74593 16.1594 3.69996 16.0484 3.61812 15.9665C3.53629 15.8847 3.4253 15.8387 3.30957 15.8387H1.86578C1.3116 15.8387 0.860401 15.3942 0.860401 14.8479C0.860401 14.3016 1.31131 13.8571 1.86578 13.8571H11.9746C14.4607 13.7648 14.4659 10.2224 11.9746 10.1279L11.9743 10.1282Z"/>
        </svg>

    </div>
  );
}

export default RenameIcon;
