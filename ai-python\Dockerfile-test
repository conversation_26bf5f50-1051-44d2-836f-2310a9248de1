# Start from the Python base image
FROM pybase_image:latest

# Set environment variables



ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
# Copy the requirements file first to leverage Docker caching

# Install pytest
RUN pip install pytest pytest-html

# Set the working directory
WORKDIR /app
# Copy the rest of the application code
COPY . /app/

RUN mkdir -p /src/logs/test_logs /src/reports

# Run the tests
CMD ["pytest", "--maxfail=1", "--disable-warnings", "--html=/src/reports/test_report.html"]
